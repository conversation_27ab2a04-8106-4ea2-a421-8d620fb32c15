<template>
  <div class="attach">
    <div style="margin: 10px 0">
      <div class="title">
        工程图纸
        <a-button
          type="primary"
          icon="plus"
          style="margin-left: 10px"
          v-if="!isMap"
          @click="() => onAddClick('projectImgAttach')"
        >
          新增
        </a-button>
      </div>
      <div class="attach-box">
        <div class="attach-item" v-for="(el, i) in dataSource.projectImgAttaches || []" :key="i">
          <div class="default-img" @click="() => onClick(el)">
            <div
              class="img"
              :style="getIsShowImg(el) ? { background: `#ffffff url(${el.attachUrl}) no-repeat` } : { display: 'none' }"
            ></div>

            <div class="mask">
              <a-icon type="delete" style="cursor: pointer" @click.stop="() => onDelete(el)" />
            </div>
          </div>
          <div class="name">{{ el.projectAttachName }}</div>
        </div>
      </div>
      <div class="title">
        工程面貌
        <a-button
          type="primary"
          icon="plus"
          style="margin-left: 10px"
          v-if="!isMap"
          @click="() => onAddClick('projectFaceAttach')"
        >
          新增
        </a-button>
      </div>
      <div class="attach-box">
        <div class="attach-item" v-for="(el, i) in dataSource.projectFaceAttaches || []" :key="i">
          <div class="default-img" @click="() => onClick(el)">
            <div
              class="img"
              :style="getIsShowImg(el) ? { background: `#ffffff url(${el.attachUrl}) no-repeat` } : { display: 'none' }"
            ></div>

            <div class="mask">
              <a-icon type="delete" style="cursor: pointer" @click.stop="() => onDelete(el)" />
            </div>
          </div>
          <div class="name">{{ el.projectAttachName }}</div>
        </div>
      </div>
      <div class="title">
        管理手册
        <a-button
          type="primary"
          icon="plus"
          style="margin-left: 10px"
          v-if="!isMap"
          @click="() => onAddClick('projectManualAttach')"
        >
          新增
        </a-button>
      </div>
      <div class="attach-box">
        <div class="attach-item" v-for="(el, i) in dataSource.projectManualAttaches || []" :key="i">
          <div class="default-img" @click="() => onClick(el)">
            <div
              class="img"
              :style="getIsShowImg(el) ? { background: `#ffffff url(${el.attachUrl}) no-repeat` } : { display: 'none' }"
            ></div>

            <div class="mask">
              <a-icon type="delete" style="cursor: pointer" @click.stop="() => onDelete(el)" />
            </div>
          </div>
          <div class="name">{{ el.projectAttachName }}</div>
        </div>
      </div>
    </div>

    <AddFileModal v-if="showAddModal && !isMap" title="新增" ref="AddFileModalRef" @confirm="onAddFileModalConfirm" />

    <!-- <UploadFile :isRender="false" ref="uploadFileRef" /> -->
  </div>
</template>

<script lang="jsx">
  import { getAttachList, addAttach, deleteAttach } from '@/api/common'

  import AddFileModal from '../components/AddFileModal.vue'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'ProjectAttach',
    components: { AddFileModal, UploadFile },
    props: {
      projectId: {},
      isMap: {},
    },
    data() {
      return {
        showAddModal: false,
        displayCode: '',
        dataSource: {},
      }
    },
    computed: {},
    watch: {},
    created() {
      this.getList()
      console.log('********** 212 ismap 附件', this.isMap)
    },
    methods: {
      getIsShowImg(el) {
        let arr = el.attachUrl.split('.')
        let type = arr[arr.length - 1]
        let imgTypes = ['png', 'jpg', 'jpeg', 'gif']

        if (imgTypes.includes(type.toLocaleLowerCase())) {
          return true
        }
        return false
      },
      onClick(el) {
        window.open(el.attachUrl)
      },
      onDelete(el) {
        const names = el.projectAttachName

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk: () => {
            return deleteAttach({ projectAttachId: el.projectAttachId }).then(res => {
              this.$message.success(`删除成功`, 3)
              this.getList()
              // this.$refs.uploadFileRef.removeMinIoFile(el.attachUrl, () => {
              // })
            })
          },
          onCancel() {},
        })
      },
      onAddClick(displayCode) {
        this.displayCode = displayCode
        this.showAddModal = true
        this.$nextTick(() => this.$refs.AddFileModalRef.showModal())
      },
      onAddFileModalConfirm(params, callback) {
        this.showAddModal = false
        addAttach({
          projectAttachName: params.name,
          attachUrl: params.url,
          projectId: this.projectId,
          displayCode: this.displayCode,
        }).then(res => {
          callback()
          this.getList()
        })
      },
      getList() {
        getAttachList({
          projectId: this.projectId,
          displayCodes: ['projectImgAttach', 'projectFaceAttach', 'projectManualAttach'],
        }).then(res => {
          this.dataSource = res.data || {}
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .attach {
    height: 100%;
    overflow: auto;
  }
  .title {
    font-weight: 700;
    font-size: 18px;
    padding: 20px 16px 10px;
  }

  .attach-box {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 0 10px;
    .attach-item {
      display: flex;
      width: 120px;
      flex-direction: column;
      align-items: center;

      .default-img {
        border-radius: 5px;
        height: 120px;
        width: 120px;
        border: 1px solid #ddd;
        position: relative;
        overflow: hidden;
        cursor: pointer;
        &::before {
          position: absolute;
          content: '';
          height: 120px;
          width: 120 * 1432px / 800;
          left: 50%;
          transform: translate(-50%);
          background: url('~@/assets/images/file-occupied.png') no-repeat;
          background-size: 100% 100%;
          z-index: 0;
        }

        .img {
          background-size: 100% 100% !important;
          height: 120px;
          width: 120px;
          position: absolute;
          z-index: 1;
        }

        .mask {
          display: none;
          text-align: center;
          line-height: 118px;
          color: #fff;
          font-size: 20px;
          position: absolute;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.6);
          z-index: 3;
          cursor: default;
        }
        &:hover {
          .mask {
            display: block;
          }
        }
      }
      .name {
        width: 100%;
        // height: 30px;
        display: -webkit-box;
        overflow: hidden;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        text-align: center;
        margin: 8px 0;
      }
    }
  }
</style>
