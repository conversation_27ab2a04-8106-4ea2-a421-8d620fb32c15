<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1500"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column">
      <!-- 方案信息表格 -->
      <div style="margin-bottom: 20px;">
        <h3 style="margin-bottom: 10px; font-weight: bold;">方案信息对比</h3>
        <a-table
          :columns="infoColumns"
          :data-source="compareData"
          :pagination="false"
          size="small"
          bordered
        />
      </div>

      <!-- 图表对比区域 -->
      <div style="flex: 1; display: flex; flex-direction: column;">
        <h3 style="margin-bottom: 10px; font-weight: bold;">数据对比</h3>

        <!-- 第一行：雨量图表和水位图表 -->
        <div :style="{height: calculateChartRowHeight(), display: 'flex', gap: '10px', marginBottom: '10px'}">
          <div style="width: 50%;">
            <RainfallChart :chartData="rainfallChartData" :chartHeight="calculateChartRowHeight()" />
          </div>
          <div style="width: 50%;">
            <WaterLevelChart :chartData="waterLevelChartData" :chartHeight="calculateChartRowHeight()" />
          </div>
        </div>

        <!-- 第二行：入库流量图表和供水流量图表 -->
        <div :style="{height: calculateChartRowHeight(), display: 'flex', gap: '10px', marginBottom: '10px'}">
          <div style="width: 50%;">
            <InflowChart :chartData="inflowChartData" :chartHeight="calculateChartRowHeight()" />
          </div>
          <div style="width: 50%;">
            <SupplyFlowChart :chartData="supplyFlowChartData" :chartHeight="calculateChartRowHeight()" />
          </div>
        </div>

        <!-- 第三行：泄洪流量图表 -->
        <div :style="{height: calculateChartRowHeight(), display: 'flex', gap: '10px'}">
          <div style="width: 100%;">
            <FloodFlowChart :chartData="floodFlowChartData" :chartHeight="calculateChartRowHeight()" />
          </div>
        </div>
      </div>
    </div>
    
    <template slot="footer">
      <a-button @click="cancel">关闭</a-button>
    </template>
  </ant-modal>
</template>

<script>
import { getDispRes } from '../../services'
import AntModal from '@/components/pt/dialog/AntModal'
import RainfallChart from './RainfallChart.vue'
import WaterLevelChart from './WaterLevelChart.vue'
import InflowChart from './InflowChart.vue'
import SupplyFlowChart from './SupplyFlowChart.vue'
import FloodFlowChart from './FloodFlowChart.vue'

export default {
  name: 'CompareModal',
  components: {
    AntModal,
    RainfallChart,
    WaterLevelChart,
    InflowChart,
    SupplyFlowChart,
    FloodFlowChart,
  },
  data() {
    return {
      open: false,
      modalLoading: false,
      modalTitle: '方案对比',
      compareRecords: [], // 对比的两个方案记录
      compareData: [], // 方案信息对比数据
      rainfallChartData: [],
      waterLevelChartData: [],
      inflowChartData: [],
      supplyFlowChartData: [],
      floodFlowChartData: [],
      
      // 方案信息表格列定义
      infoColumns: [
        {
          title: '方案名',
          dataIndex: 'caseName',
          key: 'caseName',
          width: 200,
        },
        {
          title: '方案编码',
          dataIndex: 'caseCode',
          key: 'caseCode',
          width: 220,
        },
        {
          title: '预报时段',
          dataIndex: 'forecastPeriod',
          key: 'forecastPeriod',
          width: 300,
        },
        {
          title: '调度方式',
          dataIndex: 'dispatchMethod',
          key: 'dispatchMethod',
          width: 120,
        },
      ],
    }
  },
  methods: {
    // 取消按钮
    cancel() {
      this.open = false
      this.$emit('close')
    },
    
    // 显示对比弹窗
    async handleShow(records) {
      if (records.length < 2) {
        this.$message.error('请至少选择两个方案进行对比')
        return
      }
      
      this.open = true
      this.modalLoading = true
      this.compareRecords = records
      
      try {
        // 获取所有方案的详细数据
        const promises = records.map(record => 
          getDispRes({ resvrDispId: record.resvrDispId })
        )
        const responses = await Promise.all(promises)
        const dataList = responses.map(res => res.data)
        
        // 构建方案信息对比数据
        this.buildCompareData(dataList)
        
        // 构建图表数据
        this.buildChartData(dataList)
        
        this.modalLoading = false
      } catch (error) {
        this.modalLoading = false
        this.$message.error('获取方案数据失败')
        console.error(error)
      }
    },
    
    // 构建方案信息对比数据
    buildCompareData(dataList) {
      this.compareData = dataList.map((data, index) => ({
        key: `scheme${index + 1}`,
        caseName: data.caseName,
        caseCode: data.caseCode,
        forecastPeriod: `${data.startTime} - ${data.endTime}`,
        dispatchMethod: this.getDispatchMethodName(data.dispathType),
      }))
    },
    
    // 获取调度方式名称
    getDispatchMethodName(type) {
      const typeMap = {
        1: '现状调度',
        2: '推荐调度',
        3: '手动调度'
      }
      return typeMap[type] || '未知'
    },
    
    // 构建图表数据
    buildChartData(dataList) {
      const allLists = dataList.map(data => data.resvrDispResList || [])
      const caseNames = dataList.map(data => data.caseName)
      
      // 雨量数据（柱状图）
      this.rainfallChartData = this.buildRainfallData(allLists, caseNames)
      
      // 水位数据（折线图）
      this.waterLevelChartData = this.buildLineData(allLists, caseNames, 'wlv', '水位')
      
      // 入库流量数据（折线图）
      this.inflowChartData = this.buildLineData(allLists, caseNames, 'inflow', '入库流量')
      
      // 供水流量数据（折线图）
      this.supplyFlowChartData = this.buildLineData(allLists, caseNames, 'outflow', '供水流量')
      
      // 泄洪流量数据（折线图）
      this.floodFlowChartData = this.buildLineData(allLists, caseNames, 'floodflow', '泄洪流量')
    },
    
    // 构建雨量数据（包含时段雨量和累计降雨量）
    buildRainfallData(allLists, caseNames) {
      const result = []
      
      // 为每个方案构建时段雨量数据
      allLists.forEach((list, index) => {
        const rainData = {
          name: `${caseNames[index]}-时段雨量`,
          data: list.map(el => [el.tm, el.rain || 0]),
          type: 'bar'
        }
        result.push(rainData)
      })
      
      // 为每个方案构建累计降雨量数据
      allLists.forEach((list, index) => {
        const rainData = result[index] // 获取对应的时段雨量数据
        const sumRainData = {
          name: `${caseNames[index]}-累计降雨量`,
          data: rainData.data.map((el, idx) => {
            const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
            return [el[0], +sum.toFixed(1)]
          }),
          type: 'line'
        }
        result.push(sumRainData)
      })
      
      return result
    },
    
    // 构建折线图数据
    buildLineData(allLists, caseNames, field, label) {
      return allLists.map((list, index) => ({
        name: `${caseNames[index]}-${label}`,
        data: list.map(el => [el.tm, el[field] || 0]),
      }))
    },

    // 计算图表行高度
    calculateChartRowHeight() {
      const schemeCount = this.compareRecords.length
      const baseHeight = 280 // 基础高度
      
      // 根据方案数量计算额外高度
      if (schemeCount <= 2) {
        return baseHeight + 'px'
      } else if (schemeCount <= 4) {
        return (baseHeight + (schemeCount - 2) * 30) + 'px'
      } else {
        return (baseHeight + 60 + Math.floor((schemeCount - 4) / 3) * 25) + 'px'
      }
    },
  },
}
</script>

<style lang="less" scoped>
::v-deep .modal-content {
  height: 100%;
}

::v-deep .ant-table-small > .ant-table-content > .ant-table-body {
  margin: 0;
}

::v-deep .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}
</style>
