<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button v-if="!isMap" style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">水库代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">水库名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="水库所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="左下角经度(°)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.lowLeftLong"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.lowLeftLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="左下角维度(°)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.lowLeftLat"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.lowLeftLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="右上角经度(°)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.upRightLong"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.upRightLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="右上角维度(°)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.upRightLat"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.upRightLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="水库所在位置">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="水库类型">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.resType"
                placeholder="请选择"
                :options="resTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ resTypeOptions.find(el => el.value == data.resType)?.label }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="工程等别">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engGrad"
                placeholder="请选择"
                :options="engGradOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ engGradOptions.find(el => el.value == data.engGrad)?.label }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程规模">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engScal"
                placeholder="请选择"
                :options="projectScaleOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ projectScaleOptions.find(el => el.value == data.engScal)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="坝址控制流域面积(km²)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.watShedArea"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.watShedArea }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="防洪高水位(m)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.uppLevFlco"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.uppLevFlco }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="正常蓄水位(m)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.normWatLev"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.normWatLev }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="正常蓄水位相应水面面积(km²)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.normPoolStagArea"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.normPoolStagArea }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="正常蓄水位相应库容(10⁴m³)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.normPoolStagCap"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.normPoolStagCap }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="主汛期防洪限制水位(m)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.flLowLimLev"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.flLowLimLev }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="防洪限制水位库容(10⁴m³)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.flLowLimLevCap"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.flLowLimLevCap }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="死水位(m)">
              <a-input-number v-if="type == 'edit'" v-model="form.deadLev" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.deadLev }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="总库容(10⁴m³)">
              <a-input-number v-if="type == 'edit'" v-model="form.totCap" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.totCap }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="兴利库容(10⁴m³)">
              <a-input-number v-if="type == 'edit'" v-model="form.benResCap" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.benResCap }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="死库容(10⁴m³)">
              <a-input-number v-if="type == 'edit'" v-model="form.deadCap" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.deadCap }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="调洪库容(10⁴m³)">
              <a-input-number v-if="type == 'edit'" v-model="form.storFlCap" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.storFlCap }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="防洪库容(10⁴m³)">
              <a-input-number v-if="type == 'edit'" v-model="form.flcoCap" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.flcoCap }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程建设情况">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engStat"
                placeholder="请选择"
                :options="engStatOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ engStatOptions.find(el => el.value == data.engStat)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="开工时间" prop="startDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.startDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.startDate }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="建成时间" prop="compDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.compDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.compDate }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="归口管理部门" prop="admDep">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.admDep"
                placeholder="请选择"
                :options="admDepOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ admDepOptions.find(el => el.value == data.admDep)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" prop="note" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getRes, updateRes } from './services'
  import { updateListFromStr } from '@/utils/util.js'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
      isMap: {},
    },
    data() {
      return {
        loading: false,
        data: {},

        resTypeOptions: [], //水库类型
        projectScaleOptions: [], //工程规模
        admDepOptions: [], //归口管理部门

        engGradOptions: [], //工程等别
        engStatOptions: [], //工程建设情况

        type: 'detail',
        form: {
          admDep: '',
          benResCap: undefined,
          compDate: '',
          deadCap: undefined,
          deadLev: undefined,
          engGrad: '',
          engScal: '',
          engStat: '',
          flLowLimLev: undefined,
          flLowLimLevCap: undefined,
          flcoCap: undefined,
          id: undefined,
          lowLeftLat: '',
          lowLeftLong: '',
          normPoolStagArea: undefined,
          normPoolStagCap: undefined,
          normWatLev: undefined,
          note: '',
          projectId: undefined,
          resType: '',
          startDate: '',
          storFlCap: undefined,
          totCap: undefined,
          upRightLat: '',
          upRightLong: '',
          uppLevFlco: undefined,
          watShedArea: undefined,
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('resType').then(res => {
          this.resTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('projectScale').then(res => {
          this.projectScaleOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('admDep').then(res => {
          this.admDepOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        //工程等别
        getOptions('projectWait').then(res => {
          this.engGradOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        // 工程建设情况-engStat
        getOptions('engStat').then(res => {
          this.engStatOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getRes({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            resType: res.data.resType ? `${res.data.resType}` : undefined,
            engGrad: res.data.engGrad ? `${res.data.engGrad}` : undefined,
            engScal: res.data.engScal ? `${res.data.engScal}` : undefined,

            admDep: res.data.admDep ? `${res.data.admDep}` : undefined,
            engStat: res.data.engStat ? `${res.data.engStat}` : undefined,
            // pumpType: res.data.pumpType ? `${res.data.pumpType}` : undefined,
          }
        })
      },

      onBtnClick() {
        if (this.type == 'edit') {
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateRes(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  @import url('~@/assets/styles/basic-info.less');
</style>
