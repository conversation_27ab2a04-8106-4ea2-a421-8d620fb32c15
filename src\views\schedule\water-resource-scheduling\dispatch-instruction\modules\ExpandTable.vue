<template>
  <div
    class="expand-table-container"
    @wheel="handleWheel"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <VxeTable
      ref="vxeTableRef"
      :isShowTableHeader="false"
      :tableKey="tableKey"
      :columns="columns"
      :tableData="list"
      :loading="loading"
      :isShowSize="false"
      :isShowColumns="false"
      :isShowFull="false"
      :autoHeight="true"
      :rowConfig="{ isHover: false }"
      min-height="120px"
      max-height="240px"
      size="small"
    ></VxeTable>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import { getOperateCmdByDispatch } from '../../operation-instruction/services'

  export default {
    name: 'ExpandTable',
    components: { VxeTable },
    props: ['row'],
    data() {
      return {
        tableKey: 1,
        loading: false,
        list: [],
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '操作令编号',
            field: 'operateCode',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '操作任务',
            field: 'operateTask',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          {
            title: '操作人',
            field: 'operateName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '监护人',
            field: 'guardianName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '操作时间',
            field: 'operateTime',
            minWidth: 180,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                if (row.startDate && row.endDate) {
                  return `${row.startDate} ~ ${row.endDate}`
                }
                return row.operateDate || '-'
              },
            },
          },
          {
            title: '操作',
            field: 'action',
            width: 80,
            align: 'center',
            slots: {
              default: ({ row }) => {
                return (
                  <span>
                    <a onClick={() => this.handleView(row)}>查看</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {
      // 确保表格渲染完成后设置正确的滚动行为
      this.$nextTick(() => {
        this.setupScrollBehavior()
        // 延迟再次设置，确保其他组件初始化完成后样式仍然生效
        setTimeout(() => {
          this.setupScrollBehavior()
        }, 100)
      })
    },
    methods: {
      getList() {
        this.loading = true

        // 根据调度指令编码查询操作指令
        const params = {
          cmdCode: this.row.cmdCode,
          pageNum: 1,
          pageSize: 10
        }

        getOperateCmdByDispatch(params).then(res => {
          if (res.success) {
            this.list = res.data.data || []
          } else {
            console.error('获取操作指令失败:', res.message)
            this.list = []
          }
          this.loading = false
          // 数据加载完成后重新设置滚动行为
          this.$nextTick(() => {
            this.setupScrollBehavior()
          })
        }).catch(err => {
          console.error('获取操作指令失败:', err)
          this.list = []
          this.loading = false
          this.$nextTick(() => {
            this.setupScrollBehavior()
          })
        })
      },

      // 查看操作指令详情
      handleView(row) {
        // 触发父组件的查看操作指令详情事件
        this.$emit('viewOperateCmd', row)
      },

      // 设置滚动行为
      setupScrollBehavior() {
        const tableBodyWrapper = this.$el.querySelector('.vxe-table--body-wrapper')
        if (tableBodyWrapper) {
          // 强制设置滚动容器样式，确保在生产环境中也能生效
          tableBodyWrapper.style.setProperty('overflow-y', 'auto', 'important')
          tableBodyWrapper.style.setProperty('overflow-x', 'hidden', 'important')
          tableBodyWrapper.style.setProperty('min-height', '120px', 'important')
          tableBodyWrapper.style.setProperty('max-height', '240px', 'important')
          tableBodyWrapper.style.setProperty('position', 'relative', 'important')

          // 强制重新计算滚动高度
          tableBodyWrapper.style.height = 'auto'
          this.$nextTick(() => {
            const computedHeight = tableBodyWrapper.scrollHeight
            if (computedHeight > 240) {
              tableBodyWrapper.style.setProperty('height', '240px', 'important')
            }
          })
        }

        // 同时设置表格容器的样式
        const vxeGrid = this.$el.querySelector('.vxe-grid')
        if (vxeGrid) {
          vxeGrid.style.setProperty('min-height', '120px', 'important')
          vxeGrid.style.setProperty('max-height', '240px', 'important')
        }

        const vxeTable = this.$el.querySelector('.vxe-table')
        if (vxeTable) {
          vxeTable.style.setProperty('min-height', '120px', 'important')
          vxeTable.style.setProperty('max-height', '240px', 'important')
        }

        // 设置容器本身的样式
        if (this.$el) {
          this.$el.style.setProperty('min-height', '120px', 'important')
          this.$el.style.setProperty('max-height', '240px', 'important')
        }

        // 触发VxeTable重新计算
        if (this.$refs.vxeTableRef) {
          this.$nextTick(() => {
            this.$refs.vxeTableRef.recalculate()
          })
        }
      },

      // 处理滚轮事件，智能处理滚动冒泡
      handleWheel(event) {
        const tableBodyWrapper = this.$el.querySelector('.vxe-table--body-wrapper')
        if (!tableBodyWrapper) {
          return
        }

        const scrollTop = tableBodyWrapper.scrollTop
        const scrollHeight = tableBodyWrapper.scrollHeight
        const clientHeight = tableBodyWrapper.clientHeight
        const deltaY = event.deltaY

        // 检查是否真的需要滚动
        const hasOverflow = this.list.length > 4 || scrollHeight > clientHeight

        // 如果确实不需要滚动，让事件冒泡到父级
        if (!hasOverflow) {
          return
        }

        // 有溢出内容，需要智能处理滚动
        event.preventDefault()
        event.stopPropagation()

        // 检查是否到达边界
        const atTop = scrollTop <= 0
        const atBottom = scrollTop >= scrollHeight - clientHeight

        // 如果在边界且继续向边界方向滚动，则手动触发父级滚动
        if ((atTop && deltaY < 0) || (atBottom && deltaY > 0)) {
          const parentScrollable = this.findScrollableParent(this.$el)
          if (parentScrollable) {
            parentScrollable.scrollTop += deltaY
          }
          return
        }

        // 在表格内部正常滚动
        const newScrollTop = scrollTop + deltaY
        if (newScrollTop >= 0 && newScrollTop <= scrollHeight - clientHeight) {
          tableBodyWrapper.scrollTop = newScrollTop
        } else if (newScrollTop < 0) {
          tableBodyWrapper.scrollTop = 0
        } else {
          tableBodyWrapper.scrollTop = scrollHeight - clientHeight
        }
      },

      // 查找可滚动的父元素
      findScrollableParent(element) {
        let parent = element.parentElement
        while (parent && parent !== document.body) {
          const style = window.getComputedStyle(parent)
          const overflowY = style.overflowY
          const hasScrollbar = parent.scrollHeight > parent.clientHeight

          if ((overflowY === 'auto' || overflowY === 'scroll') && hasScrollbar) {
            return parent
          }
          parent = parent.parentElement
        }
        return document.documentElement || document.body
      },

      // 鼠标进入时添加焦点样式
      handleMouseEnter() {
        this.$el.classList.add('expand-table-focused')
      },

      // 鼠标离开时移除焦点样式
      handleMouseLeave() {
        this.$el.classList.remove('expand-table-focused')
      },
    },
  }
</script>

<style lang="less" scoped>
  .expand-table-container {
    min-height: 120px !important;
    height: 240px !important;
    max-height: 240px !important;
    position: relative !important;
    overflow: hidden !important;
    border: 1px solid #e8e8e8 !important;
    border-radius: 4px !important;

    // 焦点状态样式
    &.expand-table-focused {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
    }
  }

  ::v-deep .vxe-table-box-content {
    .sortable-column-demo.vxe-grid {
      padding-bottom: 0px !important;
      margin-bottom: 0px !important;
    }
  }

  // 确保表格主体有正确的滚动行为
  .expand-table-container ::v-deep .vxe-table--body-wrapper,
  .expand-table-container ::v-deep .vxe-table--render-default .vxe-table--body-wrapper,
  .expand-table-container ::v-deep .vxe-grid .vxe-table--body-wrapper {
    min-height: 120px !important;
    max-height: 240px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    position: relative !important;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px !important;
    }

    &::-webkit-scrollbar-track {
      background: #f5f5f5 !important;
      border-radius: 3px !important;
    }

    &::-webkit-scrollbar-thumb {
      background: #d9d9d9 !important;
      border-radius: 3px !important;

      &:hover {
        background: #bfbfbf !important;
      }

      &:active {
        background: #999999 !important;
      }
    }
  }

  // 确保表格内容区域正确显示
  .expand-table-container ::v-deep .vxe-table--render-default .vxe-table--body,
  .expand-table-container ::v-deep .vxe-table--body {
    position: relative !important;
  }

  // 确保VxeTable组件本身的高度设置
  .expand-table-container ::v-deep .vxe-grid,
  .expand-table-container ::v-deep .vxe-table {
    min-height: 120px !important;
    max-height: 240px !important;
  }

  // 确保表格容器的高度设置
  .expand-table-container ::v-deep .vxe-table-box,
  .expand-table-container ::v-deep .vxe-table-box-content {
    min-height: 120px !important;
    max-height: 240px !important;
  }
</style>
