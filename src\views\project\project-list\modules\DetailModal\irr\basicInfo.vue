<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button v-if="!isMap" style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">灌区代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">灌区名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="灌区范围" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="左下角经度(°)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.lowLeftLong"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.lowLeftLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="左下角维度(°)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.lowLeftLat"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.lowLeftLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="右上角经度(°)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.upRightLong"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.upRightLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="右上角维度(°)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.upRightLat"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.upRightLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="跨界类型">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.crOverType"
                placeholder="请选择"
                :options="crOverTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ crOverTypeOptions.find(el => el.value == data.crOverType)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <!-- <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="灌区范围">              
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="主要水源工程类型">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.majWasoType"
                placeholder="请选择"
                :options="majWasoTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ majWasoTypeOptions.find(el => el.value == data.majWasoType)?.label }}
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="补充水源工程类型">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.supWasoType"
                mode="multiple"
                :filter-option="filterOption"
                :maxTagCount="1"
                placeholder="请选择"
                :options="majWasoTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{
                  majWasoTypeOptions
                    .filter(item => data.supWasoType?.includes(item.value))
                    .map(item => item.label)
                    .join(', ')
                }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程规模">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engScal"
                placeholder="请选择"
                :options="projectScaleOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ projectScaleOptions.find(el => el.value == data.engScal)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计灌溉面积(亩)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.desIrrArea"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.desIrrArea }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="备注" prop="note" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getIrr, updateIrr } from './services'
  import { updateListFromStr } from '@/utils/util.js'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
      isMap: {},
    },
    data() {
      return {
        loading: false,
        data: {},

        crOverTypeOptions: [], //跨界类型
        projectScaleOptions: [], //工程规模
        majWasoTypeOptions: [], //主要水源工程类型
        // supWasoTypeOptions: [], //补充水源工程类型

        type: 'detail',
        form: {
          crOverType: '',
          desIrrArea: undefined,
          engScal: '',
          id: undefined,
          irrRang: '',
          lowLeftLat: '',
          lowLeftLong: '',
          majWasoType: '',
          note: '',
          projectId: undefined,
          supWasoType: '',
          upRightLat: '',
          upRightLong: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('crOverType').then(res => {
          this.crOverTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('engScal').then(res => {
          this.projectScaleOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('majWasoType').then(res => {
          this.majWasoTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        // getOptions('supWasoType').then(res => {
        //   this.supWasoTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        // })
      },
      getDataSource() {
        getIrr({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            supWasoType: res.data?.supWasoType ? updateListFromStr(res.data?.supWasoType) : undefined,
            crOverType: res.data.crOverType ? `${res.data.crOverType}` : undefined,
            engScal: res.data.engScal ? `${res.data.engScal}` : undefined,
            majWasoType: res.data.majWasoType ? `${res.data.majWasoType}` : undefined,
          }
        })
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      onBtnClick() {
        if (this.type == 'edit') {
          this.form.supWasoType = this.form.supWasoType ? this.form.supWasoType.join(',') : ''
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateIrr(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  @import url('~@/assets/styles/basic-info.less');
</style>
