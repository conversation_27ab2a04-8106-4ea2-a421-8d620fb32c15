<template>
  <base-echart id="bar-line-echart" class="bar-line-echart" :width="width" :height="height" :option="options" />
</template>

<script>
  import BaseEchart from '@/components/Echarts/BaseEchart.vue'
  import * as echarts from 'echarts/core'

  export default {
    components: {
      BaseEchart,
    },
    props: {
      dataSource: {
        require: true,
        default: () => [
          {
            name: '',
            data: [],
          },
        ],
      },
      width: { default: '100%' },
      height: { default: '100%' },
      // 汛限水位值，默认为138.60m
      floodLimitLevel: {
        type: Number,
        default: 139.60,
      },
      // 正常蓄水位值，默认为140.6m
      normalWaterLevel: {
        type: Number,
        default: 140.6,
      },
    },
    data() {
      return {}
    },
    computed: {
      options() {
        return this.getOptions(this.dataSource)
      },
    },

    methods: {
      getOptions(dataSource) {
        // 检测所有流量数据的最大值
        const flowTypes = ['入库流量', '出库流量', '供水流量', '泄洪流量']
        let maxFlowValue = 0
        
        dataSource.forEach(item => {
          if (flowTypes.includes(item.name) && item.name !== '水位') {
            item.data.forEach(dataPoint => {
              const value = dataPoint[1]
              if (value !== null && value !== undefined && !isNaN(value)) {
                maxFlowValue = Math.max(maxFlowValue, Math.abs(value))
              }
            })
          }
        })
        
        // 判断是否所有流量数据都小于0.5
        const useFixedInterval = maxFlowValue < 1.5
        
        const option = {
          grid: [
            // 配置第一个柱状图的位置
            {
              left: '9%',
              right: '9%',
              top: '11%',
              height: '32%',
            },
            // 配置第二个折线图位置
            {
              left: '9%',
              right: '9%',
              top: '53%',
              height: '35%',
            },
          ],
          tooltip: {
            confine: true,
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
                height: 10,
              },
              crossStyle: { color: '#1664FF' },
              lineStyle: { color: '#1664FF' },
            },

            // formatter函数动态修改tooltip样式
            formatter: function (params) {
              if (params) {
                let htmlStr = ''
                htmlStr += params[0].name.replace(/\-/g, '/') + '<br/>' //x轴的名称
                
                // 定义tooltip显示顺序
                const displayOrder = ['时段雨量', '累计降雨量', '汛限水位', '正常蓄水位', '水位', '入库流量', '供水流量', '泄洪流量']
                
                // 将params按照显示顺序重新排序
                const sortedParams = []
                displayOrder.forEach(seriesName => {
                  const param = params.find(p => p.seriesName === seriesName)
                  if (param) {
                    sortedParams.push(param)
                  }
                })
                
                function getUnit(seriesName) {
                  switch (seriesName) {
                    case '时段雨量':
                      return 'mm/h'
                    case '累计降雨量':
                      return 'mm'
                    case '水位':
                      return 'm'
                    case '入库流量':
                      return 'm³/s'
                    case '出库流量':
                      return 'm³/s'
                    case '供水流量':
                      return 'm³/s'
                    case '泄洪流量':
                      return 'm³/s'
                    case '汛限水位':
                      return 'm'
                    case '正常蓄水位':
                      return 'm'
                    default:
                      return
                  }
                }
                
                for (var i = 0; i < sortedParams.length; i++) {
                  let param = sortedParams[i] // 使用排序后的参数
                  let seriesName = param.seriesName //图例名称
                  let value = param.value[1] === null ? '-' : param.value[1] //y轴值
                  let color = param.color //图例颜色

                  htmlStr += `
                    <div style="background:rgba(255,255,255,0.8); border-radius:4px 4px 4px 4px; padding:5px 8px; margin-top:4px; display:flex; align-items:center; justify-content:space-between">
                      <span style="margin-right:30px">
                        <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                        ${seriesName}
                      </span>
                      <span>
                        ${value} ${getUnit(seriesName)}
                      </span>
                    </div>
                    `
                }
                return htmlStr
              } else {
                return
              }
            },
            backgroundColor: '#F4F7FC ',
            borderWidth: 0,
            borderColor: '#cccccc',
            padding: 10,
            textStyle: {
              color: '#000',
              fontSize: 12,
              fontWeight: 500,
              align: 'left',
            },
          },
          legend: {
            show: true,
            x: 'center',
            y: '1',
            data: ['时段雨量', '累计降雨量', '汛限水位', '正常蓄水位', '水位', '入库流量', '出库流量', '供水流量', '泄洪流量'],

            textStyle: {
              fontSize: 12,
            },
          },
          // 将上下两个tootip合成一个
          axisPointer: {
            link: { xAxisIndex: 'all' },
          },
          xAxis: [
            {
              type: 'category',
              position: 'top',
              scale: true,
              axisLabel: {
                show: false,
              },
              axisTick: {
                alignWithLabel: true,
                show: false,
              },
              axisLine: {
                show: false,
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: 'dashed',
                  color: '#BBB',
                },
              },
            },
            {
              gridIndex: 1,
              type: 'category',
              scale: true,
              axisTick: { 
                alignWithLabel: true,
                show: false,
              },
              axisLine: {
                show: false,
              }, 
            },
          ],
          yAxis: [
            {
              type: 'value',
              name: '时段雨量(mm/h)',
              nameLocation: 'middle',
              nameGap: 60,
              nameRotate: 270,
              inverse: true,
              nameTextStyle: {
                fontSize: 12,
                // padding: [0, 0, 0, -100], // 上右下左与原位置距离
              },
              axisLabel: {
                fontSize: 12,
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: 'dashed',
                  color: '#000',
                },
              },
              scale: true,
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed',
                  color: '#cccccc',
                },
              },
              alignTicks: true, // 配置多坐标轴标签对齐
              splitNumber: 4, //设置坐标轴的分割段数
            },
            {
              type: 'value',
              name: '累计降雨量(mm)',
              inverse: true,
              nameLocation: 'middle',
              nameGap: 60,
              nameRotate: 270,
              nameTextStyle: {
                // padding: [-140, 0, 0, 0], // 上右下左与原位置距离
                fontSize: 12,
              },
              axisLabel: {
                fontSize: 12,
              },
              scale: true,
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed',
                  color: '#cccccc',
                },
              },
              alignTicks: true, // 配置多坐标轴标签对齐
              splitNumber: 4, //设置坐标轴的分割段数
            },
            {
              type: 'value',
              name: '水位(m)',
              nameLocation: 'middle',
              nameGap: 60,
              nameRotate: 270,
              nameTextStyle: {
                // padding: [0, 0, 0, -115], // 上右下左与原位置距离
                fontSize: 12,
              },
              gridIndex: 1,
              axisLabel: {
                fontSize: 12,
              },
              scale: true,
              splitLine: {
                show: false,
              },
              
              alignTicks: true, // 配置多坐标轴标签对齐
              splitNumber: 4, //设置坐标轴的分割段数
            },
            {
              type: 'value',
              name: '流量(m³/s)',
              nameLocation: 'middle',
              nameGap: 60,
              nameRotate: 270,
              nameTextStyle: {
                // padding: [0, 0, 0, -100], // 上右下左与原位置距离
                fontSize: 12,
              },
              gridIndex: 1,
              axisLabel: {
                fontSize: 12,
                formatter: function(value) {
                  // 保留2位小数显示
                  return value.toFixed(2)
                }
              },
              scale: !useFixedInterval, // 根据数据动态设置是否自动缩放
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed',
                  color: '#cccccc',
                },
              },
              alignTicks: true, // 配置多坐标轴标签对齐
              splitNumber: useFixedInterval ? 4 : 4, // 分割段数
              ...(useFixedInterval ? {
                minInterval: 0.5, // 数据小于0.5时，设置最小间隔为0.5
                interval: 0.5, // 强制设置刻度间隔为0.5
              } : {
                minInterval: 1, // 数据大于等于0.5时，使用默认最小间隔
              })
            },
          ],
          dataZoom: [
            {
              show: false,
              type: 'inside',
              xAxisIndex: [0, 1], // 显示 0 1 的数据，这个要加，不加的话，悬浮提示就会出问题
            },
          ],
          series: [], // 遍历动态填充
        }

        dataSource.forEach((item, index) => {
          if (item.name === '时段雨量') {
            option.series.push({
              name: '时段雨量',
              color: '#507EF7',
              type: 'bar',
              xAxisIndex: 0,
              yAxisIndex: 0,
              barMaxWidth: 10,
              showBackground: false,
              hoverAnimation: true, // 悬浮的动画加上
              data: item.data,
            })
          }

          if (item.name === '累计降雨量') {
            option.series.push({
              name: '累计降雨量',
              color: '#74C3F8',
              type: 'line',
              xAxisIndex: 0,
              yAxisIndex: 1,
              
              symbol: 'none',
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              hoverAnimation: true, // 悬浮的动画加上
              lineStyle: { width: 3 },
              data: item.data,
            })
          }

          if (item.name === '水位') {
            option.series.push({
              name: '水位',
              color: '#EF8432',
              type: 'line',
              xAxisIndex: 1,
              yAxisIndex: 2,
              
              symbol: 'none',
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              hoverAnimation: true, // 悬浮的动画加上
              lineStyle: { width: 3 },
              data: item.data,
            })
          }

          if (item.name === '入库流量') {
            option.series.push({
              name: '入库流量',
              color: '#1E3A8A', // 深蓝色
              type: 'line',
              xAxisIndex: 1,
              yAxisIndex: 3,
              
              symbol: 'none',
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              hoverAnimation: true, // 悬浮的动画加上
              lineStyle: { width: 3 },
              data: item.data,
            })
          }

          if (item.name === '出库流量') {
            option.series.push({
              name: '出库流量',
              color: '#A871E3',
              type: 'line',
              xAxisIndex: 1,
              yAxisIndex: 3,
              
              symbol: 'none',
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              hoverAnimation: true, // 悬浮的动画加上
              lineStyle: { width: 3 },
              data: item.data,
            })
          }

          if (item.name === '供水流量') {
            option.series.push({
              name: '供水流量',
              color: '#3CB371', // 自定义绿色
              type: 'line',
              xAxisIndex: 1,
              yAxisIndex: 3,
              
              symbol: 'none',
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              hoverAnimation: true,
              lineStyle: { width: 3 },
              data: item.data,
            })
          }

          if (item.name === '泄洪流量') {
            option.series.push({
              name: '泄洪流量',
              color: '#FF69B4', // 自定义粉色
              type: 'line',
              xAxisIndex: 1,
              yAxisIndex: 3,
              
              symbol: 'none',
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              hoverAnimation: true,
              lineStyle: { width: 3 },
              data: item.data,
            })
          }
        })

        // 添加汛限水位虚线 - 显示在下方水位图表中
        // 获取时间轴数据用于汛限水位线
        const timeAxisData = dataSource.length > 0 && dataSource[0].data.length > 0
          ? dataSource[0].data.map(item => item[0])
          : []

        if (timeAxisData.length > 0) {
          // 创建汛限水位线数据 - 所有时间点都是相同的汛限水位值
          const floodLimitData = timeAxisData.map(time => [time, this.floodLimitLevel])

          option.series.push({
            name: '汛限水位',
            color: '#ffe88a', // 淡黄色
            type: 'line',
            xAxisIndex: 1, // 使用下方图表的x轴
            yAxisIndex: 2, // 使用水位的y轴（第3个y轴，索引为2）
            symbol: 'none',
            showSymbol: false,
            lineStyle: {
              width: 2,
              type: 'dashed' // 虚线样式
            },
            data: floodLimitData,
            z: 10 // 确保汛限水位线显示在其他线条之上
          })

          // 创建正常蓄水位线数据 - 所有时间点都是相同的正常蓄水位值
          const normalWaterLevelData = timeAxisData.map(time => [time, this.normalWaterLevel])

          option.series.push({
            name: '正常蓄水位',
            color: '#54ed9f', // 淡绿色
            type: 'line',
            xAxisIndex: 1, // 使用下方图表的x轴
            yAxisIndex: 2, // 使用水位的y轴（第3个y轴，索引为2）
            symbol: 'none',
            showSymbol: false,
            lineStyle: {
              width: 2,
              type: 'dashed' // 虚线样式
            },
            data: normalWaterLevelData,
            z: 10 // 确保正常蓄水位线显示在其他线条之上
          })
        }

        return option
      },
    },
  }
</script>
<style lang="less" scoped></style>
