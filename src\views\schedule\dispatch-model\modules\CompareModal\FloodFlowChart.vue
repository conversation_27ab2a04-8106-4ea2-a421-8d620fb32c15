<template>
  <div style="height: 100%; position: relative; display: flex; flex-direction: column;">
    <div style="height: 30px; padding: 4px 0; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;">
      <h4 style="font-weight: bold; margin: 0;">泄洪流量对比</h4>
    </div>
    
    <!-- 图表容器 -->
    <div 
      ref="scrollContainer"
      style="flex: 1; overflow: hidden; border: 1px solid #f0f0f0; border-radius: 4px;"
    >
      <div 
        ref="chartContainer" 
        style="height: 100%; width: 100%;"
      ></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'FloodFlowChart',
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    chartHeight: {
      type: String,
      default: '280px'
    }
  },
  data() {
    return {
      chart: null,
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart()
      },
      deep: true,
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
    },

    updateChart() {
      if (!this.chart || !this.chartData.length) return

      const option = {
        tooltip: {
          confine: true,
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: (params) => {
            let result = params[0].axisValue + '<br/>'
            params.forEach(param => {
              result += `${param.marker}${param.seriesName}: ${param.value}m³/s<br/>`
            })
            return result
          }
        },
        legend: {
          data: this.chartData.map(item => item.name),
          top: 10,
          left: 'left',
          type: 'scroll',  // 支持横向滚动
          orient: 'horizontal',  // 水平排列
          itemWidth: 25,
          itemHeight: 14,
          itemGap: 10,
          padding: [5, 10],
          textStyle: {
            fontSize: 11
          },
          pageIconSize: 12,
          pageButtonItemGap: 3
        },
        grid: {
          left: 60,
          right: 60,
          bottom: 80,
          top: this.calculateGridTop(),
          containLabel: false
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.chartData[0]?.data.map(item => item[0] || '').filter(item => item) || [],
          axisLabel: {
            formatter: function(value) {
              // 显示完整的日期时间，分两行显示
              if (!value) return ''
              const parts = value.toString().split(' ')
              return parts[0] + '\n' + (parts[1] || '')
            },
            interval: this.calculateXAxisInterval(), // 根据数据量动态设置显示间隔
            rotate: 0,
            fontSize: 10,
            margin: 15,
            lineHeight: 16
          },
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value',
          name: '流量(m³/s)',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 12
          }
        },
        series: this.chartData.map((item, index) => ({
          name: item.name,
          type: 'line',
          data: item.data.map(d => d[1]),
          smooth: false,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            color: index === 0 ? '#fc8452' : '#9a60b4',
            width: 2
          },
          itemStyle: {
            color: index === 0 ? '#fc8452' : '#9a60b4'
          }
        }))
      }
      
      this.chart.setOption(option)
      
      // 触发图表重绘
      this.$nextTick(() => {
        this.chart.resize()
      })
    },

    // 计算X轴标签显示间隔
    calculateXAxisInterval() {
      const dataLength = this.chartData[0]?.data.length || 0

      // 根据数据量动态计算间隔，确保标签不会过于密集
      if (dataLength <= 24) {
        return 3 // 24个数据点以内，显示所有标签
      } else if (dataLength <= 72) {
        return 7 // 72个数据点以内，每3个显示一个
      } else if (dataLength <= 168) {
        return 14 // 一周数据以内，每6个显示一个
      } else if (dataLength <= 720) {
        return 70 // 一个月数据以内，每12个显示一个
      } else {
        return 150 // 更长时间，每24个显示一个
      }
    },

    // 计算grid top值，根据图例项数量动态调整
    calculateGridTop() {
      const legendCount = this.chartData.length
      const baseTop = 50  // 基础top值
      
      // 根据图例项数量计算额外空间
      if (legendCount <= 4) {
        return baseTop
      } else if (legendCount <= 8) {
        return baseTop + 20
      } else if (legendCount <= 12) {
        return baseTop + 40
      } else {
        return baseTop + 60
      }
    },

    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
  }
}
</script>

<style lang="less" scoped>
</style>
