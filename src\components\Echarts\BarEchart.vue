<template>
  <base-echart id="bar-echart" class="bar-echart" :width="width" :height="height" :option="options" />
</template>

<script lang="jsx">
  import BaseEchart from './BaseEchart.vue'
  import * as echarts from 'echarts/core'

  const colors = [
    '#42d4f4',
    '#3cb44b',
    '#4363d8',
    '#911eb4',
    '#bfef45',
    '#fabed4',
    '#469990',
    '#dcbeff',
    '#9A6324',
    '#aaffc3',
    '#808000',
    '#000075',
    '#a9a9a9',
  ]

  export default {
    components: {
      BaseEchart,
    },
    props: {
      dataSource: {
        require: true,
        default: () => [
          {
            name: '',
            data: [],
          },
        ],
      },
      custom: { default: () => {} },
      width: { default: '100%' },
      height: { default: '300px' },
    },
    data() {
      return {}
    },
    mounted() {},
    computed: {
      options() {
        return this.getOptions(this.dataSource, this.custom)
      },
    },
    methods: {
      getOptions(data, custom) {
        let {
          shortValue = true, // 缩写坐标值
          xLabel = '', // x轴名称
          yLabel = '', //y轴名称
          yUnit = '', //y轴单位
          rYUnit = '', // 右侧y轴单位
          rYLabel = '', // 右侧y轴名称
          legend = false, // 图例
          singleValue = true,
          inverse,
          dataZoom = true,
        } = custom

        let xAxisParams = {
          type: 'category',
          data: data.length ? data[0].data.map(i => i[0]) : [],
          axisLabel: {
            textStyle: {
              color: '#000',
            },
          },
        }
        let yAxisParams = {
          type: 'value',
        }

        let legendParams = {
          top: 5,
          left: '50%',
        }

        if (inverse) {
          let temp
          temp = xAxisParams
          xAxisParams = yAxisParams
          yAxisParams = temp

          temp = xLabel
          xLabel = yLabel
          yLabel = temp

          legendParams = {
            bottom: 0,
            left: 'center',
          }

          xAxisParams.splitLine = {
            show: false,
          }
          xAxisParams.axisLabel = {
            textStyle: {
              color: 'rgba(246, 255, 255, 0.7)',
            },
            formatter(t) {
              if (shortValue) {
                if (t > Math.pow(10, 8) - 1) {
                  return t / Math.pow(10, 9) + '亿'
                }
                if (t > Math.pow(10, 5) - 1) {
                  return t / 10000 + '万'
                }
              }
              if (yUnit) {
                return `${t}${yUnit}`
              }
              return t
            },
          }
        }

        const maxValue = Math.max(...data.map(item => item.value))
        const yAxisMax = maxValue <= 3 ? 3 : maxValue * 1.2
        const option = {
          title: {
            top: 5,
            left: 5,
            // text: yLabel,
            textAlign: 'left',
            textStyle: {
              color: 'rgba(246, 255, 255, 0.7)',
              fontSize: 12,
              fontWeight: 400,
            },
          },
          grid: {
            left: '5%',
            right: xLabel ? '10%' : '4%',
            bottom: inverse ? '12%' : '15%',
            top: inverse ? '12%' : '10%',
            containLabel: true,
          },
          color: colors,
          tooltip: {
            // confine: true,
            trigger: 'axis',
            // backgroundColor: 'rgba(0,0,0,0.6)', //通过设置rgba调节背景颜色与透明度
            borderWidth: 0,
            textStyle: {
              color: '#000',
            },
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
            // formatter: params => {
            //   let dataStr = `<div><p style="font-weight:bold;margin:0 5px 5px;">${params[0].name}</p></div>`;
            //   params.forEach(item => {
            //     dataStr += `<div>
            //    <div style="margin: 0 8px;">
            //      <span style="display:inline-block;margin-right:5px;width:10px;height:10px;background-color:${
            //        item.color.colorStops[0].color
            //      };border-radius:2px;"></span>
            //      <span>${singleValue  true? item.name : item.seriesName}</span>
            //      <span style="float:right;color:#fff;margin-left:20px;">${
            //        item.data
            //      }</span>
            //    </div>
            //  </div>`;
            //   });
            //   return dataStr;
            // }
          },
          xAxis: {
            name: (data.length && xLabel) || '',
            nameTextStyle: {
              padding: [0, 0, 0, 15],
              color: '#000',
              fontSize: 12,
              fontWeight: 400,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: '#000',
              },
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#000',
              },
            },
            z: 10,
            ...xAxisParams,
          },
          yAxis: [
            {
              name: yLabel || '',
              nameTextStyle: {
                padding: [0, 0, 0, 30], // 左内边距设为30，使单位名称离左边有一定距离
              },
              axisPointer: { show: false },
              axisLine: { show: false },
              axisTick: { show: false },
              splitLine: {
                lineStyle: { type: 'dashed', color: '#BBB' },
              },
              max: yAxisMax, // 动态设置最大值
              min: 0, // 动态设置最小值
              axisLabel: {
                textStyle: { color: '#000000' },
                formatter(t) {
                  if (shortValue) {
                    if (t > Math.pow(10, 8) - 1) {
                      return t / Math.pow(10, 9) + '亿'
                    }
                    if (t > Math.pow(10, 5) - 1) {
                      return t / 10000 + '万'
                    }
                  }
                  if (yUnit) {
                    return `${t}${yUnit}`
                  }
                  return t
                },
              },
              ...yAxisParams,
            },
            {
              name: rYLabel || '',
              nameTextStyle: {
                padding: [0, 30, 0, 0], // 右内边距设为30，使右侧Y轴单位名称离右边有一定距离
              },
              position: 'right',
              axisPointer: { show: false },
              axisLine: { show: false },
              splitLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                textStyle: { color: '#000' },
                formatter(t) {
                  if (shortValue) {
                    if (t > Math.pow(10, 8) - 1) {
                      return t / Math.pow(10, 9) + '亿'
                    }
                    if (t > Math.pow(10, 4) - 1) {
                      return t / 10000 + '万'
                    }
                  }
                  if (yUnit) {
                    return `${t}${rYUnit}`
                  }
                  return t
                },
              },
            },
          ],

          dataZoom: [
            {
              show: !!dataZoom,
              moveHandleSize: 12,
              height: 25,
              width: '80%',
              left: '10%',
              bottom: '6%',
            },
            {
              type: 'inside',
            },
          ],
          legend: {
            show: !!custom?.legend,
            icon: 'roundRect',
            itemWidth: 12,
            itemHeight: 12,
            textStyle: {
              color: '#000',
            },
            ...legendParams,
            ...legend,
          },
          series: data.map((item, index) => {
            return {
              type: 'bar',
              barMaxWidth: 8,
              showBackground: true,
              // backgroundStyle: {
              //   color: 'rgba(255, 255, 255, 0.05)'
              // },
              name: item.name,
              stack: item.stack,
              yAxisIndex: item?.yAxisIndex || 0,
              itemStyle: {
                borderRadius: [2, 2, 0, 0], //（顺时针左上，右上，右下，左下）
                color: item.color || 'rgba(64, 93, 249, 1)',

                // index === 0
                //   ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //       {
                //         offset: 0,
                //         color: '#1978E5'
                //       },
                //       {
                //         offset: 1,
                //         color: '#10274B'
                //       }
                //     ])
                //   : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //       {
                //         offset: 0,
                //         color: 'rgba(243, 213, 156, 0.85)'
                //       },
                //       {
                //         offset: 1,
                //         color: 'rgba(206, 159, 65, 0.85)'
                //       }
                //     ])
              },
              emphasis: {
                itemStyle: {
                  color: 'rgba(64, 93, 249, 0.6)',
                  // index === 0
                  //   ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  //       {
                  //         offset: 0,
                  //         color: '#00D5FF'
                  //       },
                  //       {
                  //         offset: 1,
                  //         color: 'rgba(9,111,165, 1)'
                  //       }
                  //     ])
                  //   : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  //       { offset: 0, color: '#CFA144' },
                  //       { offset: 1, color: '#CFA144' }
                  //     ])
                },
              },
              data: item.data.map(d => d[1]),
            }
          }),
        }
        return option
      },
    },
  }
</script>

<style scoped></style>
