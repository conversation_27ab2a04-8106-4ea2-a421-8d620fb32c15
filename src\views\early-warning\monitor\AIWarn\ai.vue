<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="对象名称">
        <a-input v-model="queryParam.objectName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="预警时间">
        <a-range-picker
          allow-clear
          :value="takeEffect"
          format="YYYY-MM-DD"
          formatValue="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>
      <template #table>
        <!-- :otherHeight="84"
          :isShowTableHeader="false" -->
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" v-if="selectIds.length != 0" @click="eliminateAlarms()">消除报警</a-button>
            <!-- <a-button type="danger" v-if="selectIds.length != 0" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button> -->
          </div>
        </VxeTable>
      </template>
    </VxeTableForm>
    <HistoryAIMonitor
      v-if="showHistoryMonitor"
      ref="historyMonitorRef"
      :isFromAIWarn="isFromAIWarn"
      @ok="onOperation"
      @close="showHistoryMonitor = false"
    />
    <!-- URL弹窗 -->
    <a-modal
      v-model:visible="showUrlDialog"
      title="附件预览"
      :footer="null"
      :loading="modalLoading"
      @cancel="showUrlDialog = false"
      :width="1000"
      :height="880"
    >
      <div style="padding: 10px">
        <div class="image-container" ref="imageContainer">
          <img :src="currentUrl" style="max-width: 100%; max-height: 700px" alt="图片预览" @load="handleImageLoad" />
          <div
            class="annotation-rect"
            v-if="showAnnotation"
            :style="{
              left: `${annotationPosition.x * 100}%`,
              top: `${annotationPosition.y * 100}%`,
              width: `${annotationPosition.w * 100}%`,
              height: `${annotationPosition.h * 100}%`,
            }"
          ></div>
          <div
            class="annotation-name"
            v-if="showAnnotation"
            :style="{
              left: `${annotationPosition.x * 100}%`,
              top: `${annotationPosition.y * 100}%`,
              width: `${annotationPosition.w * 100}%`,
            }"
          >
            {{ currentType }}
            <!-- {{ currentType }}: {{ currentName }} -->
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="jsx">
  import { getWarnProjectCategory, getAIWarnEventList, eventClearAIAlarm } from '../services'
  import TreeGeneral from '@/components/TreeGeneral'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import HistoryAIMonitor from '../modules/HistoryAIMonitor.vue'

  export default {
    name: 'monitor',
    components: {
      VxeTable,
      VxeTableForm,
      TreeGeneral,
      HistoryAIMonitor,
    },
    props: {
      isFromAIWarn: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        modalLoading: false,
        currentName: '漂浮物名称',
        currentType: '漂浮物类型',
        coordinates: null, // { h: '0.245833', w: '0.150781', x: '0.368750', y: '0.391667' },
        annotationPosition: {},
        showAnnotation: false,
        treeTabKey: '1',
        currentKeys: [],
        defaultExpandedKeys: [3],
        showHistoryMonitor: false,
        showUrlDialog: false,
        currentUrl: '',
        list: [],
        tableTitle: 'AI预警监控',
        isChecked: false,
        selectIds: [],
        names: [],
        loading: false,
        total: 0,

        takeEffect: [],
        queryParam: {
          groupId: null,
          objectCode: '',
          objectName: '',
          pageNum: 1,
          pageSize: 10,
          projectId: undefined,
          warnTimeLower: null,
          warnTimeUpper: null,
        },
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '预警编号',
            field: 'eventNo',
            minWidth: 165,
            showOverflow: 'tooltip',
          },
          {
            title: '预警对象',
            field: 'objectName',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          {
            title: '预警分组',
            field: 'groupName',
            minWidth: 130,
            showOverflow: 'tooltip',
          },
          {
            title: '对象类别',
            field: 'objectCategoryName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '预警消息',
            field: 'message',
            minWidth: 280,
            showOverflow: 'tooltip',
          },
          {
            title: '当前状态',
            field: 'status',
            minWidth: 80,
            slots: {
              default: ({ row }) => {
                let statusPointColor = 'common-status-abnormal'
                let statusText = '正在报警'
                if (row.status == 2) {
                  statusPointColor = 'common-status-completed'
                  statusText = '已消除'
                } else if (row.status == 3) {
                  statusPointColor = 'common-status-incomplete'
                  statusText = '已失效'
                }
                return (
                  <div class='common-status-box'>
                    <i class={['common-status-icon', statusPointColor]}></i>
                    <span>{statusText}</span>
                  </div>
                )
              },
            },
          },
          {
            title: '预警时间',
            field: 'eventDate',
            minWidth: 147,
          },
          {
            title: '附件',
            field: 'url',
            minWidth: 50,
            slots: {
              default: ({ row }) => {
                return row.url ? (
                  <a onClick={() => this.showCurrentUrlDialog(row)}>附件</a>
                ) : (
                  <a style={'color: #999'} href='javascript:;'>
                    无
                  </a>
                )

                // <div>
                //   <a-button
                //     type='primary'
                //     size='small'
                //     onClick={() => this.handleUrl(row)}
                //   >
                //     查看
                //   </a-button>

                // </div>
              },
            },
          },
          //   {
          //     title: '操作',
          //     field: 'operate',
          //     width: 125,
          //     slots: {
          //       default: ({ row, rowIndex }) => {
          //         if (!this.isFromAIWarn) {
          //           console.log(this.isFromAIWarn)
          //           return (
          //             <span>
          //               <a onClick={() => this.handleHistory(row)}>查看</a>
          //               <a-divider type='vertical' />
          //               <a disabled={row.status != 1} onClick={() => this.eliminateAlarms('one', row)}>
          //                 消除报警
          //               </a>
          //             </span>
          //           )
          //         }
          //         return null
          //       },
          //     },
          //   },
        ],
      }
    },
    created() {
      // // 获取工程树
      // getWarnProjectCategory().then(res => {
      //   this.projectCategory.dataSource = res?.data
      // })
      this.getList()
    },
    methods: {
      showCurrentUrlDialog(row) {
        this.currentUrl = ''
        this.modalLoading = true
        this.currentUrl = row?.url?.replace('http://thj.zjhhzk.cn:11123', 'http://192.168.2.11:6040')
        console.log(this.currentUrl)
        this.currentName = row?.objectName
        this.currentType = row?.eventType
        this.coordinates = row?.rect ? JSON.parse(row?.rect) : null
        // 重置标注状态
        this.showAnnotation = false
        this.annotationPosition = {}
        setTimeout(() => {
          this.showUrlDialog = true
          // 如果有坐标数据，预先设置标注位置
          if (this.coordinates) {
            this.annotationPosition = {
              x: parseFloat(this.coordinates?.x),
              y: parseFloat(this.coordinates?.y),
              w: parseFloat(this.coordinates?.w),
              h: parseFloat(this.coordinates?.h),
            }
            // 延迟设置showAnnotation为true，确保DOM已更新
            this.$nextTick(() => {
              this.showAnnotation = true
            })
          }
        }, 500)
      },
      handleImageLoad() {
        // 只有当coordinates不为null且showAnnotation为false时才设置标注
        // 这避免了与showCurrentUrlDialog方法中的标注逻辑冲突
        if (this.coordinates && !this.showAnnotation) {
          this.annotationPosition = {
            x: parseFloat(this.coordinates?.x),
            y: parseFloat(this.coordinates?.y),
            w: parseFloat(this.coordinates?.w),
            h: parseFloat(this.coordinates?.h),
          }
          this.showAnnotation = true
        }
        // 如果coordinates为null，确保隐藏标注
        if (!this.coordinates) {
          this.showAnnotation = false
        }
      },

      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.loading = true
        getAIWarnEventList(this.queryParam).then(response => {
          if (response.data == null) {
            this.loading = false
            return
          }
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.eventId)
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.takeEffect = []

        this.queryParam = {
          ...this.queryParam,
          objectName: '',
          warnTimeLower: null,
          warnTimeUpper: null,
          pageNum: 1,
        }
        this.handleQuery()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.warnTimeLower = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') : null
        this.queryParam.warnTimeUpper = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') : null
      },
      // 树加载完成后
      // onTreeMounted(data) {
      //   this.queryParam.projectId = data[0].key
      //   this.getList()
      // },
      // clickTreeNode(node) {
      //   const key = node.$options.propsData.dataRef.key
      //   this.queryParam.projectId = key
      //   this.queryParam.pageNum = 1
      //   this.getList()
      // },
      handleHistory(record) {
        this.showHistoryMonitor = true
        this.$nextTick(() => this.$refs.historyMonitorRef.historyMonitor(record))
      },
      eliminateAlarms(type, row) {
        let ids
        if (type == 'one') {
          ids = row.eventId
        } else {
          ids = this.selectIds.join(',')
        }
        var that = this
        this.$confirm({
          title: '确认消除警报?',
          content: '请确认是否消除警报',
          onOk() {
            eventClearAIAlarm({ eventIds: ids }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功`, 3)
                that.onOperation()
                that.selectIds = []
              }
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperation() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  .show-url {
    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    .ant-modal-body {
      flex: 1;
      overflow: auto;
      padding: 16px;
    }
  }

  .image-container {
    position: relative;
    display: inline-block;
  }

  .annotation-rect {
    position: absolute;
    border: 2px solid red;
    box-sizing: border-box;
    pointer-events: none;
    background-color: rgba(255, 0, 0, 0.1);
  }

  .annotation-name {
    position: absolute;
    color: red;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    white-space: nowrap;
    pointer-events: none;
    margin-top: 0px; /* 向上移动25px，避免与标注框重叠 */
    padding: 2px 5px;
    /* background-color: rgba(255, 255, 255, 0.7); */
    border-radius: 3px;
    transform: translateY(-100%); /* 额外向上移动自身高度 */
  }
</style>
