<template>
  <div style="flex: 1; display: flex; flex-direction: column;">
    <!-- Tab栏 (仅在推荐调度且有多方案时显示) -->
    <div v-if="showTabs" style="padding: 0 20px; margin-bottom: 16px; flex: 1; display: flex; flex-direction: column;">
      <!-- 在多方案模式下，如果还在加载中，显示全局加载状态 -->
      <div v-if="loading" style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center">
        <div style="text-align: center;">
          <a-spin size="large" />
          <div style="margin-top: 16px; font-size: 16px; color: #666;">
            正在加载模型结果，请稍候...
          </div>
        </div>
      </div>
      
      <!-- 如果有错误信息，显示错误状态 -->
      <div 
        v-else-if="errorInfo" 
        style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center"
      >
        <a-result status="error" :sub-title="errorInfo"></a-result>
      </div>
      
      <!-- 所有方案加载完成后，显示Tab栏 -->
      <div v-else>
        <!-- <div> -->
        <a-spin :spinning="tabLoading">
          <a-tabs v-model="activeTab" @change="handleTabChange">
            <a-tab-pane key="compare" tab="方案对比">
              <CompareResult
                v-if="activeTab === 'compare' && compareData.length > 0"
                :compareData="compareData"
                :chartData="compareChartData"
              />
            </a-tab-pane>
            <a-tab-pane key="planA" tab="A方案">
              <SingleResult
                v-if="activeTab === 'planA' && resultDataA"
                :resultData="resultDataA"
                :chartData="chartDataA"
              />
            </a-tab-pane>
            <a-tab-pane key="planB" tab="B方案">
              <SingleResult
                v-if="activeTab === 'planB' && resultDataB"
                :resultData="resultDataB"
                :chartData="chartDataB"
              />
            </a-tab-pane>
            <a-tab-pane key="planC" tab="C方案">
              <SingleResult
                v-if="activeTab === 'planC' && resultDataC"
                :resultData="resultDataC"
                :chartData="chartDataC"
              />
            </a-tab-pane>
            <a-tab-pane key="planD" tab="D方案">
              <SingleResult
                v-if="activeTab === 'planD' && resultDataD"
                :resultData="resultDataD"
                :chartData="chartDataD"
              />
            </a-tab-pane>
          </a-tabs>
        </a-spin>
      </div>
    </div>

    <!-- 原有的单方案结果展示 (非推荐调度或推荐调度的单方案展示) -->
    <div v-else style="flex: 1; display: flex">
      <div v-if="loading" style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center">
        <a-spin></a-spin>
      </div>

      <div
        v-else-if="!!errorInfo"
        style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center"
      >
        <a-result status="error" :sub-title="errorInfo"></a-result>
      </div>

      <SingleResult
        v-else-if="!!resultData"
        :resultData="resultData"
        :chartData="chartData"
      />
    </div>
  </div>
</template>

<script lang="jsx">
  import { getDispRes, getInWaterRes } from '../../services'
  // import { SocketClient } from '@/utils/sockClient.js'
  import BarAndLineMixChart from './BarAndLineMixChart.vue'
  import ResultTable from './ResultTable.vue'
  import SingleResult from './SingleResult.vue'
  import CompareResult from './CompareResult.vue'
  import moment from 'moment'

  export default {
    name: 'Result',
    props: ['baseInfo', 'outflowData', 'inWaterId'],
    components: {
      BarAndLineMixChart,
      ResultTable,
      SingleResult,
      CompareResult,
    },
    data() {
      return {
        loading: false,
        socketIns: null,
        errorInfo: null,
        resultData: null,
        chartData: [],
        pollingTimer: null, // 轮询定时器
        pollingCount: 0, // 轮询次数
        maxPollingCount: 30, // 最大轮询次数（5分钟，每10秒一次）
        inWaterData: null, // 来水预报方案数据
        
        // 多方案相关数据
        activeTab: 'compare',
        showTabs: false,
        tabLoading: false,
        resultDataA: null,
        resultDataB: null,
        resultDataC: null,
        resultDataD: null,
        chartDataA: [],
        chartDataB: [],
        chartDataC: [],
        chartDataD: [],
        compareData: [],
        compareChartData: [],
        allResultsLoaded: false,
      }
    },
    computed: {
      // 判断是否显示tab栏
      isRecommendDispatch() {
        return this.baseInfo.dispatchMethod === 2 && this.outflowData && this.outflowData.resvrDispIds
      }
    },
    created() {
      this.loading = true
      this.$emit('update:isDisabledBtn', true)
      this.errorInfo = null

      // 判断是否为推荐调度且有多方案
      if (this.isRecommendDispatch) {
        this.showTabs = true
        this.loadMultipleResults()
      } else {
        // 单方案逻辑
        this.loadSingleResult()
      }
    },
    mounted() {},
    beforeDestroy() {
      // this.socketIns.disconnect()
      this.clearPolling()
    },
    methods: {
      // 加载单方案结果
      loadSingleResult() {
        if (this.outflowData && this.outflowData.resvrDispId) {
          this.loadModelResult()
        }
      },

      // 加载多方案结果
      loadMultipleResults() {
        const resvrDispIds = this.outflowData.resvrDispIds
        console.log('多个方案的模型id', resvrDispIds)
        if (!resvrDispIds || resvrDispIds.length !== 4) {
          this.errorInfo = '方案数据不完整'
          this.$emit('update:isDisabledBtn', false)
          this.loading = false
          return
        }

        // 为四个方案分别加载数据
        const promises = resvrDispIds.map((id, index) => this.loadModelResultById(id, index))
        
        Promise.all(promises).then(() => {
          // 所有方案都成功完成
          this.allResultsLoaded = true
          this.buildCompareData()
          this.$emit('update:isDisabledBtn', false)
          this.loading = false
        }).catch(err => {
          console.error('加载多方案结果失败:', err)
          this.errorInfo = err.message || '加载方案结果失败'
          this.$emit('update:isDisabledBtn', false)
          this.loading = false
        })
      },

      // 根据ID加载单个模型结果
      loadModelResultById(resvrDispId, index) {
        return new Promise((resolve, reject) => {
          const params = { resvrDispId }
          
          getDispRes(params).then(res => {
            if (res.success && res.data) {
              if (res.data.modelStatus === 2) {
                // 模型执行成功
                this.processModelResultById(res.data, index)
                resolve()
              } else if (res.data.modelStatus === 9) {
                // 模型执行异常
                reject(new Error(`方案${String.fromCharCode(65 + index)}执行异常: ${res.data.modelErr}`))
              } else {
                // 模型执行中，开始轮询
                this.startPollingById(resvrDispId, index, resolve, reject)
                // 不立即resolve，等待轮询完成
              }
            } else {
              reject(new Error(`获取方案${String.fromCharCode(65 + index)}结果失败`))
            }
          }).catch(err => {
            reject(err)
          })
        })
      },

      // 处理单个模型结果
      processModelResultById(data, index) {
        const resultKey = `resultData${String.fromCharCode(65 + index)}`
        const chartKey = `chartData${String.fromCharCode(65 + index)}`
        
        // 处理结果数据
        this[resultKey] = {
          dispathType: data.dispathType || 0,
          caseCode: data.caseCode || '',
          caseName: data.caseName || '',
          startTime: data.startTime || '',
          endTime: data.endTime || '',
          startWaterLevel: data.startWaterLevel || 0,
          endWaterLevel: data.endWaterLevel || 0,
          maxWaterLevel: data.maxWaterLevel || 0,
          minWaterLevel: data.minWaterLevel || 0,
          totalRainfall: data.sumRainfall || 0,
          totalInflowVolume: data.sumInWater || 0,
          totalOutflowVolume: data.sumOutWater || 0,
          totalSupplyVolume: data.sumOutWater || 0,
          totalFloodVolume: data.sumFloodWater || 0,
          peakFlow: data.peakDischarge || 0,
          peakTime: data.peakDate || '暂无时间',
          resvrDispResList: data.resvrDispResList || [],
          inWater: data.inWater || {}
        }

        // 处理图表数据
        this[chartKey] = this.processChartData(data.resvrDispResList || [])
      },

      // Tab切换处理
      handleTabChange(activeKey) {
        this.tabLoading = true
        this.activeTab = activeKey
        setTimeout(() => {
          this.tabLoading = false
        }, 300)
      },

      // 构建对比数据
      buildCompareData() {
        if (!this.allResultsLoaded) return

        const results = [this.resultDataA, this.resultDataB, this.resultDataC, this.resultDataD].filter(Boolean)
        
        // 构建方案信息对比数据
        this.compareData = results.map((data, index) => ({
          key: `scheme${index + 1}`,
          caseName: data.caseName,
          caseCode: data.caseCode,
          forecastPeriod: `${data.startTime} - ${data.endTime}`,
          dispatchMethod: this.getDispatchMethodName(data.dispathType),
        }))

        // 构建对比图表数据
        this.compareChartData = this.buildCompareChartData(results)
      },

      // 构建对比图表数据
      buildCompareChartData(results) {
        const allLists = results.map(result => result.resvrDispResList || [])
        const caseNames = results.map(result => result.caseName)
        
        return [
          {
            type: 'rainfall',
            data: this.buildRainfallData(allLists, caseNames)
          },
          {
            type: 'waterLevel',
            data: this.buildLineData(allLists, caseNames, 'wlv', '水位')
          },
          {
            type: 'inflow',
            data: this.buildLineData(allLists, caseNames, 'inflow', '入库流量')
          },
          {
            type: 'supplyFlow',
            data: this.buildLineData(allLists, caseNames, 'outflow', '供水流量')
          },
          {
            type: 'floodFlow',
            data: this.buildLineData(allLists, caseNames, 'floodflow', '泄洪流量')
          }
        ]
      },

      // 构建雨量数据
      buildRainfallData(allLists, caseNames) {
        const result = []
        
        // 为每个方案构建时段雨量数据
        allLists.forEach((list, index) => {
          const rainData = {
            name: `${caseNames[index]}-时段雨量`,
            data: list.map(el => [el.tm, el.rain || 0]),
            type: 'bar'
          }
          result.push(rainData)
        })
        
        // 为每个方案构建累计降雨量数据
        allLists.forEach((list, index) => {
          const rainData = result[index]
          const sumRainData = {
            name: `${caseNames[index]}-累计降雨量`,
            data: rainData.data.map((el, idx) => {
              const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
              return [el[0], +sum.toFixed(1)]
            }),
            type: 'line'
          }
          result.push(sumRainData)
        })
        
        return result
      },

      // 构建折线图数据
      buildLineData(allLists, caseNames, field, label) {
        return allLists.map((list, index) => ({
          name: `${caseNames[index]}-${label}`,
          data: list.map(el => [el.tm, el[field] || 0]),
        }))
      },

      // 获取调度方式名称
      getDispatchMethodName(type) {
        const typeMap = {
          1: '现状调度',
          2: '推荐调度',
          3: '手动调度'
        }
        return typeMap[type] || '未知'
      },

      // 保留原有的单方案相关方法
      loadModelResult() {
        const params = {
          resvrDispId: this.outflowData.resvrDispId
        }

        getDispRes(params).then(res => {
          if (res.success && res.data) {
            // 检查模型状态
            if (res.data.modelStatus === 1) {
              // 模型执行中，开始轮询
              this.startPolling()
              return // 不设置loading为false，继续显示加载状态
            } else if (res.data.modelStatus === 9) {
              // 模型执行异常
              this.errorInfo = '模型执行异常: ' + (res.data.modelErr || '未知错误')
            } else if (res.data.modelStatus === 2) {
              // 模型执行成功
              this.processModelResult(res.data)
            } else {
              this.errorInfo = '未知的模型状态: ' + res.data.modelStatus
            }
          } else {
            this.errorInfo = '获取模型结果失败: ' + (res.message || '未知错误')
          }
          this.$emit('update:isDisabledBtn', false)
          this.loading = false
        }).catch(err => {
          console.error('调用模型结果接口失败:', err)
          this.errorInfo = '调用模型结果接口失败'
          this.$emit('update:isDisabledBtn', false)
          this.loading = false
        })
      },

      // 处理真实的模型结果数据
      processModelResult(data) {
        // 处理基本信息
        this.resultData = {
          dispathType: data.dispathType || 0,
          caseCode: data.caseCode || '',
          caseName: data.caseName || '',
          startTime: data.startTime || '',
          endTime: data.endTime || '',
          startWaterLevel: data.startWaterLevel || 0,
          endWaterLevel: data.endWaterLevel || 0,
          maxWaterLevel: data.maxWaterLevel || 0,
          minWaterLevel: data.minWaterLevel || 0,
          totalRainfall: data.sumRainfall || 0,
          totalInflowVolume: data.sumInWater || 0,
          totalOutflowVolume: data.sumOutWater || 0,
          totalSupplyVolume: data.sumOutWater || 0, // 根据实际数据结构调整
          totalFloodVolume: data.sumFloodWater || 0,
          peakFlow: data.peakDischarge || 0, // 需要从resvrDispResList中计算
          peakTime: data.peakDate || '暂无时间',
          resvrDispResList: data.resvrDispResList || [],
          // 来水信息
          inWater: data.inWater || {}
        }

        // 处理图表数据
        this.processChartData(data.resvrDispResList || [])
      },

      // 处理图表数据
      processChartData(resvrDispResList) {
        console.log('resvrDispResList', resvrDispResList)
        const rainData = {
          name: '时段雨量',
          data: resvrDispResList.map(el => [el.tm, el.rain || 0]),
        }

        const sumRainData = {
          name: '累计降雨量',
          data: resvrDispResList.map(el => [el.tm, el.sumRain || 0]),
        }

        return [
          rainData,
          sumRainData,
          {
            name: '水位',
            data: resvrDispResList.map(el => [el.tm, el.wlv || 0]),
          },
          {
            name: '入库流量',
            data: resvrDispResList.map(el => [el.tm, el.inflow || 0]),
          },
          {
            name: '供水流量',
            data: resvrDispResList.map(el => [el.tm, el.outflow || 0]), // 根据实际字段调整
          },
          {
            name: '泄洪流量',
            data: resvrDispResList.map(el => [el.tm, el.floodflow || 0]),
          },
        ]
      },
      
      // 开始轮询
      startPolling() {
        this.pollingCount = 0
        this.pollingTimer = setInterval(() => {
          this.pollingCount++
          if (this.pollingCount > this.maxPollingCount) {
            this.clearPolling()
            this.errorInfo = '模型执行超时，请稍后手动刷新查看结果'
            this.$emit('update:isDisabledBtn', false)
            this.loading = false
            return
          }

          // 重新调用接口检查状态
          this.checkModelStatus()
        }, 10000) // 每10秒检查一次
      },

      // 清除轮询
      clearPolling() {
        if (this.pollingTimer) {
          clearInterval(this.pollingTimer)
          this.pollingTimer = null
        }
      },

      // 检查模型状态
      checkModelStatus() {
        const params = {
          resvrDispId: this.outflowData.resvrDispId
        }

        getDispRes(params).then(res => {
          if (res.success && res.data) {
            if (res.data.modelStatus === 2) {
              // 模型执行成功
              this.clearPolling()
              this.processModelResult(res.data)
              this.$emit('update:isDisabledBtn', false)
              this.loading = false
            } else if (res.data.modelStatus === 9) {
              // 模型执行异常
              this.clearPolling()
              this.errorInfo = '模型执行异常: ' + (res.data.modelErr || '未知错误')
              this.$emit('update:isDisabledBtn', false)
              this.loading = false
            }
            // 如果还是执行中状态(1)，继续轮询
          }
        }).catch(err => {
          console.error('轮询检查模型状态失败:', err)
          // 轮询失败不中断，继续下次轮询
        })
      },

      // 多方案轮询方法
      startPollingById(resvrDispId, index, resolve, reject) {
        const pollCount = { count: 0 }
        const maxPolls = 30
        const pollInterval = 10000

        const poll = () => {
          pollCount.count++
          if (pollCount.count > maxPolls) {
            reject(new Error(`方案${String.fromCharCode(65 + index)}轮询超时`))
            return
          }

          getDispRes({ resvrDispId }).then(res => {
            if (res.success && res.data) {
              if (res.data.modelStatus === 2) {
                // 模型执行成功
                this.processModelResultById(res.data, index)
                resolve()
              } else if (res.data.modelStatus === 9) {
                // 模型执行异常
                reject(new Error(`方案${String.fromCharCode(65 + index)}执行异常: ${res.data.modelErr}`))
              } else if (res.data.modelStatus === 1) {
                // 继续轮询
                setTimeout(poll, pollInterval)
              }
            }
          }).catch(err => {
            console.error(`方案${String.fromCharCode(65 + index)}轮询失败:`, err)
            setTimeout(poll, pollInterval)
          })
        }

        setTimeout(poll, pollInterval)
      },

      save() {
        // 传递resvrDispId用于保存
        console.log(this.outflowData)
        const saveData = this.outflowData ?? this.outflowData.resvrDispIds
        this.$emit('saveData', saveData)
      },
    },
  }
</script>

<style lang="less" scoped>
.left {
  .item {
    display: flex;
    height: 36px;
    line-height: 24px;
    align-items: center;
    
    .label {
      // width: 120px;
      color: #4e5969;
      // text-align: right;
      font-size: 13px;
    }
    .value {
      flex: 1;
      color: #1d2129;
      font-weight: 500;
      padding-left: 8px;
      // 文字溢出隐藏
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
