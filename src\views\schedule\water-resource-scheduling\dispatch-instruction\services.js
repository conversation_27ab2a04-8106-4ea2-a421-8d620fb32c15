import request from '@/utils/request'

// 分页查询调度指令
export function getPage(data) {
  return request({
    url: '/custom/runmd/page',
    method: 'post',
    data
  })
}

// 新增调度指令
export function addDispatchInstruction(data) {
  return request({
    url: '/custom/runmd/add',
    method: 'post',
    data
  })
}

// 修改调度指令
export function editDispatchInstruction(data) {
  return request({
    url: '/custom/runmd/update',
    method: 'post',
    data
  })
}

// 根据ID获取调度指令详情
export function getDispatchInstructionById(runCmdId) {
  return request({
    url: '/custom/runmd/get',
    method: 'post',
    params: { runCmdId }
  })
}

// 审核调度指令
export function auditDispatchInstruction(data) {
  return request({
    url: '/custom/runmd/audit',
    method: 'post',
    data
  })
}

// 接收调度指令
export function receiveDispatchInstruction(runCmdId) {
  return request({
    url: '/custom/runmd/rec',
    method: 'post',
    params: { runCmdId }
  })
}

// 获取调度方案选项
export function getDispatchOptions() {
  return request({
    url: '/model/ch-sim/page',
    method: 'post',
    data: {
      pageNum: 1,
      pageSize: 999
    }
  })
}

// 获取工程选项
export function getProjectOptions() {
  return request({
    url: '/custom/dispatch-project/list',
    method: 'post'
  })
}

// 获取工程负责人选项
export function getProjectUserOptions(dispatchProjectId) {
  return request({
    url: '/custom/dispatch-project/user/list',
    method: 'post',
    params: { dispatchProjectId }
  })
}

// 工作票编码是否存在
export function checkCmdCode(params) {
  return request({
    url: '/custom/runmd/cmdCodeExist',
    method: 'get',
    params,
  })
}