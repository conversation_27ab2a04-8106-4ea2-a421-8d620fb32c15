<template>
  <div v-if="resultData" style="height: 100%; width: 100%; display: flex">
    <!-- 左侧统计信息 -->
    <div class="left" style="width: 270px; padding: 14px; border-right: 1px solid #f0f0f0">
      <div class="item">
        <div class="label">方案编码</div>
        <div class="value">{{ resultData.caseCode }}</div>
      </div>
      <div class="item">
        <div class="label">方案名称</div>
        <div class="value">{{ resultData.caseName }}</div>
      </div>
      <div class="item">
        <div class="label">开始时间</div>
        <div class="value">{{ resultData.startTime }}</div>
      </div>
      <div class="item">
        <div class="label">结束时间</div>
        <div class="value">{{ resultData.endTime }}</div>
      </div>
      <div class="item">
        <div class="label">累计降雨量:</div>
        <div class="value">{{ resultData.totalRainfall }}mm</div>
      </div>
       <div class="item">
        <div class="label">洪峰流量:</div>
        <div class="value">{{ resultData.peakFlow }}m³/s</div>
      </div>
      <div class="item">
        <div class="label">峰现时间:</div>
        <div class="value">{{ resultData.peakTime || '暂无时间' }}</div>
      </div>
      <div class="item">
        <div class="label">累计入库水量:</div>
        <div class="value">{{ resultData.totalInflowVolume }}万m³</div>
      </div>
      <div class="item">
        <div class="label">起调水位:</div>
        <div class="value">{{ resultData.startWaterLevel }}m</div>
      </div>
      <div class="item">
        <div class="label">末期水位:</div>
        <div class="value">{{ resultData.endWaterLevel }}m</div>
      </div>
      <div class="item">
        <div class="label">最高水位</div>
        <div class="value">{{ resultData.maxWaterLevel }}m</div>
      </div>
      <div class="item">
        <div class="label">最低水位</div>
        <div class="value">{{ resultData.minWaterLevel }}m</div>
      </div>
      <div class="item">
        <div class="label">累计供水量:</div>
        <div class="value">{{ resultData.totalSupplyVolume }}万m³</div>
      </div>
      <div class="item">
        <div class="label">累计泄洪量:</div>
        <div class="value">{{ resultData.totalFloodVolume }}万m³</div>
      </div>
      <div class="item">
        <div class="label">累计出库水量:</div>
        <div class="value">{{ resultData.totalOutflowVolume }}万m³</div>
      </div>
    </div>

    <!-- 右侧图表和表格 -->
    <div style="flex: 1; height: 100%">
      <div style="height: 50%">
        <BarAndLineMixChart :dataSource="chartData" />
      </div>

      <ResultTable :dataSource="resultData?.resvrDispResList || []" :resultData="resultData" style="height: 50%" />
    </div>
  </div>
</template>

<script>
import BarAndLineMixChart from './BarAndLineMixChart.vue'
import ResultTable from './ResultTable.vue'

export default {
  name: 'SingleResult',
  components: {
    BarAndLineMixChart,
    ResultTable,
  },
  props: {
    resultData: {
      type: Object,
      default: () => ({})
    },
    chartData: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style lang="less" scoped>
.left {
  .item {
    display: flex;
    height: 36px;
    line-height: 24px;
    align-items: center;
    
    .label {
      color: #4e5969;
      font-size: 13px;
    }
    .value {
      flex: 1;
      color: #1d2129;
      font-weight: 500;
      padding-left: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>