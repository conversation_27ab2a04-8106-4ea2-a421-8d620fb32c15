<template>
  <div class="common-table-page" style="display: flex; flex-direction: column; background: #ffffff; padding: 24px">
    <!-- 第一大区块 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
      <div>
        <a-select
          v-model="selectedForecast"
          style="width: 350px;"
          placeholder="请选择预报数据"
          :loading="loading"
        >
          <a-select-option v-for="item in forecastOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
      <div>
        <a-button @click="goToIncomingWaterInversion" style="margin-right: 16px; color: #4E5969; background: #fff; border-color: #E5E6EB; font-size: 14px; font-weight: 400;">
          来水反演
        </a-button>
        <a-button type="primary" style="color: #fff; background: #165DFF; font-size: 14px; font-weight: 400;" @click="goToComingWaterForecast">
          方案管理
        </a-button>
      </div>
    </div>

    <!-- 第二大区块 -->
    <div style="margin-bottom: 16px; padding-bottom: 16px; border-bottom: 1px solid #f2f3f5;">
      <!-- 上部分信息展示 -->
      <div style="display: flex; justify-content: space-between; margin-bottom: 16px;">
        <div style="display: flex;">
          <span style="color: #4E5969; font-size: 14px;">方案编码: </span>
          <span style="color: #1D2129; font-size: 14px; margin-right: 16px;">{{ dataSource?.schemeCode || '暂无数据' }}</span>
        </div>
        <div style="display: flex;">
          <span style="color: #4E5969; font-size: 14px;">预报方式: </span>
          <span style="color: #1D2129; font-size: 14px; margin-right: 16px;">{{ dataSource?.forecastType || '暂无数据' }}</span>
        </div>
        <div style="display: flex;">
          <span style="color: #4E5969; font-size: 14px;">模拟应用场景: </span>
          <span style="color: #1D2129; font-size: 14px; margin-right: 16px;">{{ dataSource?.scenario || '暂无数据' }}</span>
        </div>
        <div style="display: flex;">
          <span style="color: #4E5969; font-size: 14px;">预报时段: </span>
          <span style="color: #1D2129; font-size: 14px; margin-right: 16px;">{{ dataSource?.forecastPeriod || '暂无数据' }}</span>
        </div>
        <div style="display: flex;">
          <span style="color: #4E5969; font-size: 14px;">方案生成时间: </span>
          <span style="color: #1D2129; font-size: 14px;">{{ dataSource?.createdTime || '暂无数据' }}</span>
        </div>
      </div>

      <!-- 下部分五块信息 -->
      <div class="summary">
        <hgroup class="hgroup" style="background: #E8F3FF;">
          <i class="icon"></i>
          <div class="content">
            <h5 class="text">前三天实际降雨量</h5>
            <h2 class="num">
              {{ formatNumber(dataSource?.threeRainSum) || '0' }}
              <span class="text unit">mm</span>
            </h2>
          </div>
        </hgroup>
        <hgroup class="hgroup" style="background: #E2F6F3;">
          <i class="icon"></i>
          <div class="content">
            <h5 class="text">预报累计降雨量</h5>
            <h2 class="num">
              {{ formatNumber(dataSource?.rainSum) || '0' }}
              <span class="text unit">mm</span>
            </h2>
          </div>
        </hgroup>
        <hgroup class="hgroup" style="background: #E8EAFF;">
          <i class="icon"></i>
          <div class="content">
            <h5 class="text">预报累计来水量</h5>
            <h2 class="num">
              {{ formatNumber(dataSource?.inWaterSum) || '0' }}
              <span class="text unit">万m³</span>
            </h2>
          </div>
        </hgroup>
        <hgroup class="hgroup" style="background: #FFF0E8;">
          <i class="icon"></i>
          <div class="content">
            <h5 class="text">洪峰流量</h5>
            <h2 class="num">
              {{ formatNumber(dataSource?.peakFlow) || '0' }}
              <span class="text unit">m³/s</span>
            </h2>
          </div>
        </hgroup>
        <hgroup class="hgroup" style="background: #E6F5FA;">
          <i class="icon"></i>
          <div class="content">
            <h5 class="text">峰现时间</h5>
            <h2 class="num">
              {{ dataSource?.peakTime || '暂无时间' }}
            </h2>
          </div>
        </hgroup>
      </div>
    </div>

    <!-- 第三大区块 -->
    <div class="flood-box">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <p class="flood-tabs">
          <label class="name">来水预报</label>
        </p>
        <a @click="toggleTable" style="color: #165DFF; cursor: pointer; font-size: 14px;">
          {{ isTableExpanded ? '表格收起' : '表格展开' }}
        </a>
      </div>
      
      <div class="flood-content" :style="{ display: 'flex', flexDirection: 'row', width: '100%' }">
        <div :style="{ width: isTableExpanded ? '50%' : '100%' }">
          <ResultChart v-if="!!dataSource" :dataSource="dataSource" :scenario="'forecast'" :key="chartKey" />
        </div>
        <div v-if="isTableExpanded" style="width: 50%; margin-left: 16px;">
          <ResultTable :dataSource="dataSource?.fcsts || []" :scenario="'forecast'" :isShowTableHeader="false" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getOptions, getValueByKey } from '@/api/common'
  import { queryAutoForecast, getInWaterPage, getInWaterRes } from './services'
  import ResultChart from './components/Chart.vue'
  import ResultTable from './components/ResultTable.vue'

  export default {
    name: 'ManualForecast',
    components: { ResultChart, ResultTable },

    data() {
      return {
        selectedForecast: undefined,
        forecastOptions: [],
        dataSource: null,
        isTableExpanded: true,
        chartKey: 0,
        loading: false,
      }
    },
    computed: {},
    watch: {
      // 监听下拉框选择变化
      selectedForecast(newVal) {
        if (newVal) {
          this.getDetailData();
        }
      }
    },
    created() {
      this.getForecastOptions();
    },
    methods: {
      // 获取预报数据下拉框选项
      getForecastOptions() {
        this.loading = true;
        getInWaterPage({
          pageNum: 1,
          pageSize: 5
        }).then(res => {
          if (res.success && res.data && res.data.data) {
            // 转换数据格式为下拉框选项
            this.forecastOptions = res.data.data.map(item => ({
              label: `${item.caseName || '未命名方案'}-${item.startTime || ''} ~ ${item.endTime || ''}`,
              value: item.inWaterId,
              sourceType: item.sourceType,
              ...item
            }));

            // 默认选中第一条sourceType为1的数据
            const defaultOption = this.forecastOptions.find(item => item.sourceType === 1);
            if (defaultOption) {
              this.selectedForecast = defaultOption.value;
            } else if (this.forecastOptions.length > 0) {
              // 如果没有sourceType为1的数据，选择第一条
              this.selectedForecast = this.forecastOptions[0].value;
            }

            // 获取详细数据
            if (this.selectedForecast) {
              this.getDetailData();
            }
          }
        }).catch(err => {
          console.error('获取预报数据失败:', err);
        }).finally(() => {
          this.loading = false;
        });
      },

      // 获取详细数据
      getDetailData() {
        if (!this.selectedForecast) return;

        this.loading = true;
        getInWaterRes({
          inWaterId: this.selectedForecast
        }).then(res => {
          if (res.success && res.data) {
            // 映射数据到页面显示格式
            this.dataSource = this.mapDetailData(res.data);
          }
        }).catch(err => {
          console.error('获取详细数据失败:', err);
        }).finally(() => {
          this.loading = false;
        });
      },

      // 数据映射方法
      mapDetailData(data) {
        return {
          // 上部分信息展示
          schemeCode: data.caseCode || '',
          forecastType: this.getSourceTypeText(data.sourceType),
          scenario: this.getSceneText(data.scene),
          forecastPeriod: `${data.startTime || ''} ~ ${data.endTime || ''}`,
          createdTime: data.createdTime || '',

          // 下部分五块信息
          threeRainSum: data.beforeRainValue || 0,
          rainSum: data.rainSum || 0,
          inWaterSum: data.inWaterSum || 0,
          peakFlow: data.maxInflow || 0,
          peakTime: data.tm || '',

          // 表格和图表数据
          reals: data.reals || [], // 添加 reals 数据
          fcsts: data.res || []
        };
      },

      // 获取数据源类型文本
      getSourceTypeText(sourceType) {
        const typeMap = {
          1: '自动预报',
          2: '人工预报'
        };
        return typeMap[sourceType] || '未知';
      },

      // 获取场景文本
      getSceneText(scene) {
        const sceneMap = {
          1: '历史复演',
          2: '未来预报'
        };
        return sceneMap[scene] || '未知';
      },

      // 格式化数字，小数最多保留两位
      formatNumber(value) {
        if (value === null || value === undefined || value === '') {
          return '0';
        }
        const num = Number(value);
        if (isNaN(num)) {
          return '0';
        }
        // 如果是整数，直接返回
        if (num % 1 === 0) {
          return num.toString();
        }
        // 如果是小数，最多保留两位小数
        return num.toFixed(2).replace(/\.?0+$/, '');
      },

      toggleTable() {
        this.isTableExpanded = !this.isTableExpanded;
        this.$nextTick(() => {
          this.chartKey += 1;
        });
      },
      goToComingWaterForecast() {
        this.$router.push('/schedule/incoming-water-model-list')
      },
      goToIncomingWaterInversion() {
        this.$router.push('/schedule/incoming-water-model-Inversion')
      }
    },
  }
</script>

<style lang="less" scoped>
  .summary {
    display: flex;
    justify-content: space-between;
    .hgroup {
      width: 19%;
      padding: 24px 26px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 12px;
      
      .content {
        flex: 1;
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      
      .num {
        font-weight: 700;
        font-size: 24px;
        color: #1d2129;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 0;
      }
      .text {
        font-size: 14px;
        color: #4E5969;
        font-weight: 400;
        margin: 0;
      }
      .unit {
        margin-left: -2px;
        font-size: 14px;
      }
      .icon {
        width: 50px;
        height: 50px;
        display: inline-block;
        flex-shrink: 0;
      }
      &:nth-child(1) {
        .icon {
          background: url('@/assets/images/three-days-rain.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(2) {
        .icon {
          background: url('@/assets/images/all-rain.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(3) {
        .icon {
          background: url('@/assets/images/coming-water.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(4) {
        .icon {
          background: url('@/assets/images/flood-peak.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(5) {
        .icon {
          background: url('@/assets/images/flood-peak-time.png') 0 0 no-repeat;
          background-size: 100%;
        }
        .num {
            font-size: 15px;
        }
      }
    }
  }

  .flood-box {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .flood-tabs {
      margin: 0;
      .name {
        font-size: 20px;
        color: #1d2129;
        font-weight: 600;
      }
    }
    
    .flood-content {
      flex: 1;
    }
  }

  @font-face {
    font-family: 'AlimamaDaoLiTi';
    src: url('@/assets/font/AlimamaDaoLiTi.ttf');
  }
</style>
