<template>
  <div>
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      @cancel="cancel"
      modalWidth="1300"
      modalHeight="800"
      :footer="null"
    >
      <div slot="content">
        <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
          <a-form-item label="预警编号">
            <a-input v-model="queryParam.eventNo" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
          </a-form-item>
          <a-form-item label="预警时间">
            <a-range-picker
              allow-clear
              :value="takeEffect"
              format="YYYY-MM-DD"
              formatValue="YYYY-MM-DD"
              :placeholder="['开始时间', '结束时间']"
              @change="onRangeChange"
            />
          </a-form-item>
          <template #table>
            <VxeTable
              ref="vxeTableRef"
              tableTitle="历史预警"
              :columns="columns"
              :tableData="historyList"
              :loading="loading"
              @refresh="getHistoryList"
              :rowConfig="{ isCurrent: true, isHover: true }"
              :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
              @handlePageChange="handlePageChange"
            ></VxeTable>
          </template>
        </VxeTableForm>
      </div>
      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
      </template>
    </ant-modal>
    <a-modal
      v-model:visible="showUrlDialog"
      title="附件预览"
      :footer="null"
      :loading="modalLoading"
      @cancel="showUrlDialog = false"
      :width="1000"
      :height="880"
    >
      <div style="padding: 10px">
        <div class="image-container" ref="imageContainer">
          <img :src="currentUrl" style="max-width: 100%; max-height: 700px" alt="图片预览" @load="handleImageLoad" />
          <div
            class="annotation-rect"
            v-if="showAnnotation"
            :style="{
              left: `${annotationPosition.x * 100}%`,
              top: `${annotationPosition.y * 100}%`,
              width: `${annotationPosition.w * 100}%`,
              height: `${annotationPosition.h * 100}%`,
            }"
          ></div>
          <div
            class="annotation-name"
            v-if="showAnnotation"
            :style="{
              left: `${annotationPosition.x * 100}%`,
              top: `${annotationPosition.y * 100}%`,
              width: `${annotationPosition.w * 100}%`,
            }"
          >
            {{ currentType }}
            <!-- {{ currentType }}: {{ currentName }} -->
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script lang="jsx">
  import { getAIEventHistoryList } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'FormMaintenance',
    components: { AntModal, VxeTable, VxeTableForm },
    props: {
      isFromAIWarn: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        currentName: '漂浮物名称',
        currentType: '漂浮物类型',
        coordinates: null,
        annotationPosition: {},
        showAnnotation: false,
        currentUrl: '',
        showUrlDialog: false,
        modalLoading: false,
        formTitle: '查看历史预警',
        open: false,
        takeEffect: [],
        queryParam: {
          eventNo: undefined,
          objectId: null,
          pageNum: 1,
          pageSize: 10,
          sort: [],
          status: null,
          warnTimeLower: '',
          warnTimeUpper: '',
        },
        total: 0,
        historyList: [],
        loading: false,
        columns: [
          {
            type: 'seq',
            title: '序号',
            minWidth: 50,
            slots: {
              default: ({ rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '预警编号',
            field: 'eventNo',
            minWidth: 120,
            showOverflow: 'tooltip',
          },

          // {
          //   title: '对象类别',
          //   field: 'objectCategoryName',
          // },
          {
            title: '预警描述',
            field: 'message',
            minWidth: 600,
            showOverflow: 'tooltip',
          },
          {
            title: '当前状态',
            field: 'status',
            minWidth: 80,
            slots: {
              default: ({ row }) => {
                if (row.status == 1) {
                  return <span>正在报警</span>
                } else if (row.status == 2) {
                  return <span>已消除</span>
                } else if (row.status == 3) {
                  return <span>已失效</span>
                }
              },
            },
          },
          {
            title: '预警时间',
            field: 'createdTime',
            minWidth: 120,
          },
          {
            title: '附件',
            field: 'url',
            minWidth: 50,
            slots: {
              default: ({ row }) => {
                return row.url ? (
                  <a onClick={() => this.showCurrentUrlDialog(row)}>附件</a>
                ) : (
                  <a style={'color: #999'} href='javascript:;'>
                    无
                  </a>
                )

                // <div>
                //   <a-button
                //     type='primary'
                //     size='small'
                //     onClick={() => this.handleUrl(row)}
                //   >
                //     查看
                //   </a-button>

                // </div>
              },
            },
          },
        ],
      }
    },
    watch: {
      showUrlDialog: {
        handler(newVal, oldVal) {
          // 只在关闭弹窗时清空URL，避免打开时闪烁
          if (!newVal) {
            this.currentUrl = ''
          }
        },
        deep: true,
      },
    },
    methods: {
      showCurrentUrlDialog(row) {
        
        this.currentUrl = ''
        this.modalLoading = true
        if (this.isFromAIWarn) {
          console.log('row.url', row.url)
          this.currentUrl = row?.url?.replace('http://thj.zjhhzk.cn:11123', 'http://************:6040')
        } else {
          console.log('row.url', row.url)
          this.currentUrl = row?.url
        }
        this.currentName = row?.objectName
        this.currentType = row?.eventType
        this.coordinates = row?.rect ? JSON.parse(row?.rect) : null
        // 重置标注状态
        this.showAnnotation = false
        this.annotationPosition = {}

        // 添加100秒延迟
        setTimeout(() => {
          this.showUrlDialog = true
          // 如果有坐标数据，预先设置标注位置
          if (this.coordinates) {
            this.annotationPosition = {
              x: parseFloat(this.coordinates?.x),
              y: parseFloat(this.coordinates?.y),
              w: parseFloat(this.coordinates?.w),
              h: parseFloat(this.coordinates?.h),
            }
            // 延迟设置showAnnotation为true，确保DOM已更新
            this.$nextTick(() => {
              this.showAnnotation = true
              this.modalLoading = false
            })
          } else {
            // 如果没有坐标数据，也需要设置modalLoading为false
            this.modalLoading = false
          }
        }, 500) // 100秒延迟
      },
      handleImageLoad() {
        // 只有当coordinates不为null且showAnnotation为false时才设置标注
        // 这避免了与showCurrentUrlDialog方法中的标注逻辑冲突

        if (this.coordinates && !this.showAnnotation) {
          this.annotationPosition = {
            x: parseFloat(this.coordinates?.x),
            y: parseFloat(this.coordinates?.y),
            w: parseFloat(this.coordinates?.w),
            h: parseFloat(this.coordinates?.h),
          }
          this.showAnnotation = true
        }
        // 如果coordinates为null，确保隐藏标注
        if (!this.coordinates) {
          this.showAnnotation = false
        }
      },

      // 取消按钮
      cancel() {
        this.currentUrl = ''
        this.open = false
        this.$emit('close')
      },
      /** 新增按钮操作 */
      historyMonitor(row) {
        this.open = true
        this.queryParam.objectId = 1
        this.queryParam.objectIds = row.objectIds

        this.modalLoading = true
        this.getHistoryList()
      },
      getHistoryList() {
        getAIEventHistoryList(this.queryParam).then(res => {
          this.modalLoading = false
          this.historyList = res?.data?.data || []
          this.total = res?.data?.total
        })
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.warnTimeLower = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') : null
        this.queryParam.warnTimeUpper = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') : null
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getHistoryList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.takeEffect = []
        this.queryParam.eventNo = undefined
        this.queryParam.warnTimeLower = null
        this.queryParam.warnTimeUpper = null
        this.queryParam.pageNum = 1
        this.queryParam.pageSize = 10
        this.handleQuery()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
  ::v-deep .vxe-table-box,
  ::v-deep .vxe-table-content .vxe-table-box .vxe-table-box-content {
    height: 500px !important;
  }

  .image-container {
    position: relative;
    display: inline-block;
  }

  .annotation-rect {
    position: absolute;
    border: 2px solid red;
    box-sizing: border-box;
    pointer-events: none;
    background-color: rgba(255, 0, 0, 0.1);
  }

  .annotation-name {
    position: absolute;
    color: red;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    white-space: nowrap;
    pointer-events: none;
    margin-top: 0px; /* 向上移动25px，避免与标注框重叠 */
    padding: 2px 5px;
    /* background-color: rgba(255, 255, 255, 0.7); */
    border-radius: 3px;
    transform: translateY(-100%); /* 额外向上移动自身高度 */
  }
</style>
