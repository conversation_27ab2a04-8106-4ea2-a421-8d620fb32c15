<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1300"
    @cancel="cancel"
    modalHeight="800"
    :maskClosable="false"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column">
      <a-steps :current="step" size="small" style="padding: 4px 280px">
        <a-step title="基本信息" />
        <a-step title="出库过程" />
        <a-step title="模拟结果" />
      </a-steps>

      <keep-alive>
        <Basic
          v-if="step == 0"
          ref="basicRef"
          v-bind="$attrs"
          @saveData="saveData"
          :inWaterEchoData="inWaterEchoData"
        />
      </keep-alive>
      <OutflowProcess
        v-if="step == 1"
        ref="outflowRef"
        :baseInfo="baseInfo"
        @saveData="saveData"
        @update:processing="val => isDisabledBtn = val"
      />
      <Result
        v-if="step == 2"
        ref="resultRef"
        :baseInfo="baseInfo"
        :outflowData="outflowData"
        :isDisabledBtn.sync="isDisabledBtn"
        @saveData="saveData"
      />
    </div>
    <template slot="footer">
      <a-button @click="cancel" v-if="step === 0">取消</a-button>
      <a-button @click="preStep" v-if="step !== 0" :disabled="isDisabledBtn || step === 2">上一步</a-button>
      <a-button type="primary" @click.stop="onSubmit" :loading="loading" :disabled="isDisabledBtn">
        {{ step === 2 ? '保存' : '下一步' }}
      </a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { debounce } from 'lodash'
  // import { saveResvrDisp } from '../../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import Basic from './Basic.vue'
  import OutflowProcess from './OutflowProcess.vue'
  import Result from './Result.vue'

  export default {
    name: 'AddModal',
    components: { AntModal, Basic, OutflowProcess, Result },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '新增',
        step: 0,
        baseInfo: {},
        outflowData: {},

        isDisabledBtn: false,
        inWaterEchoData: null,
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      preStep() {
        this.step--
      },
      handleShow(params) {
        this.open = true

        if (params) {
          this.inWaterEchoData = params
        }
      },



      saveData(params) {
        if (!!params) {
          switch (this.step) {
            case 0:
              this.baseInfo = params
              this.$nextTick(() => (this.step += 1))
              break
            case 1:
              this.outflowData = params
              // 恢复按钮状态
              this.isDisabledBtn = false
              this.$nextTick(() => (this.step += 1))
              break
            case 2:
              // saveResvrDisp({ resvrDispId: params }).then(res => {
              //   this.$message.success('保存成功', 3)
              //   this.$emit('close')
              //   this.$emit('ok')
              // })
              this.$message.success('保存成功', 3)
              this.$emit('close')
              this.$emit('ok')
              break
            default:
              break
          }
        }
      },
      onSubmit() {
        // 使用防抖函数，防止快速多次点击
        this.debouncedSubmit()
      },

      // 防抖提交方法
      debouncedSubmit: debounce(function() {
        switch (this.step) {
          case 0:
            this.$refs.basicRef.save()
            break
          case 1:
            this.$refs.outflowRef.save()
            break
          case 2:
            this.$refs.resultRef.save()
            break
          default:
            break
        }
      }, 500), // 2000ms防抖延迟
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }
</style>
