<template>
  <div style="height: 100%; display: flex; flex-direction: column;">
    <!-- 方案信息表格 -->
    <div style="margin-bottom: 20px;">
      <h3 style="margin-bottom: 10px; font-weight: bold;">方案信息对比</h3>
      <a-table
        :columns="infoColumns"
        :data-source="compareData"
        :pagination="false"
        size="small"
        bordered
      />
    </div>

    <!-- 图表对比区域 -->
    <div style="flex: 1; display: flex; flex-direction: column;">
      <h3 style="margin-bottom: 10px; font-weight: bold;">数据对比</h3>

      <!-- 第一行：雨量图表和水位图表 -->
      <div style="height: 250px; display: flex; gap: 10px; margin-bottom: 10px;">
        <div style="width: 50%;">
          <RainfallChart :chartData="rainfallChartData" />
        </div>
        <div style="width: 50%;">
          <WaterLevelChart :chartData="waterLevelChartData" />
        </div>
      </div>

      <!-- 第二行：入库流量图表和供水流量图表 -->
      <div style="height: 250px; display: flex; gap: 10px; margin-bottom: 10px;">
        <div style="width: 50%;">
          <InflowChart :chartData="inflowChartData" />
        </div>
        <div style="width: 50%;">
          <SupplyFlowChart :chartData="supplyFlowChartData" />
        </div>
      </div>

      <!-- 第三行：泄洪流量图表 -->
      <div style="height: 250px; display: flex; gap: 10px;">
        <div style="width: 100%;">
          <FloodFlowChart :chartData="floodFlowChartData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RainfallChart from '../CompareModal/RainfallChart.vue'
import WaterLevelChart from '../CompareModal/WaterLevelChart.vue'
import InflowChart from '../CompareModal/InflowChart.vue'
import SupplyFlowChart from '../CompareModal/SupplyFlowChart.vue'
import FloodFlowChart from '../CompareModal/FloodFlowChart.vue'

export default {
  name: 'CompareResult',
  components: {
    RainfallChart,
    WaterLevelChart,
    InflowChart,
    SupplyFlowChart,
    FloodFlowChart,
  },
  props: {
    compareData: {
      type: Array,
      default: () => []
    },
    chartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 方案信息表格列定义
      infoColumns: [
        {
          title: '方案名',
          dataIndex: 'caseName',
          key: 'caseName',
          width: 200,
        },
        {
          title: '方案编码',
          dataIndex: 'caseCode',
          key: 'caseCode',
          width: 220,
        },
        {
          title: '预报时段',
          dataIndex: 'forecastPeriod',
          key: 'forecastPeriod',
          width: 300,
        },
        {
          title: '调度方式',
          dataIndex: 'dispatchMethod',
          key: 'dispatchMethod',
          width: 120,
        },
      ],
    }
  },
  computed: {
    // 计算雨量图表数据
    rainfallChartData() {
      return this.chartData.find(data => data.type === 'rainfall')?.data || []
    },
    // 计算水位图表数据
    waterLevelChartData() {
      return this.chartData.find(data => data.type === 'waterLevel')?.data || []
    },
    // 计算入库流量图表数据
    inflowChartData() {
      return this.chartData.find(data => data.type === 'inflow')?.data || []
    },
    // 计算供水流量图表数据
    supplyFlowChartData() {
      return this.chartData.find(data => data.type === 'supplyFlow')?.data || []
    },
    // 计算泄洪流量图表数据
    floodFlowChartData() {
      return this.chartData.find(data => data.type === 'floodFlow')?.data || []
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .ant-table-small > .ant-table-content > .ant-table-body {
  margin: 0;
}

::v-deep .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}
</style>