<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="490"
    @cancel="cancel"
    modalHeight="460"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="上传类型" prop="action">
                <a-radio-group v-model="form.action">
                  <a-radio value="1">新增</a-radio>
                  <a-radio value="2">更新</a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24" v-if="form.action == 1">
              <a-form-model-item label="洪水过程名称" prop="durationName">
                <a-input style="width: 100%" allowClear v-model="form.durationName" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24" v-if="form.action == 2">
              <a-form-model-item label="洪水过程名称" prop="floodDurationId">
                <a-select
                  show-search
                  :filter-option="filterOption"
                  placeholder="请选择"
                  v-model="form.floodDurationId"
                  style="width: 100%"
                >
                  <a-select-option v-for="(d, index) in floodDurationList" :key="index" :value="d.id">
                    {{ d.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="文件上传" prop="fileUrl">
                <div style="display: flex; position: relative">
                  <UploadFile
                    class="file-upload"
                    :fileUrl.sync="form.fileUrl"
                    :multiple="false"
                    listType="text"
                    folderName="temp"
                  />
                  <a
                    :href="reservoirCurveTplUrl"
                    style="margin-top: 6px; margin-left: 10px; left: 120px; position: absolute"
                  >
                    下载模版
                  </a>
                </div>
                <span style="margin-top: 9px; font-size: 12px">支持扩展名: .xlsx .xls</span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { uploadCstDesignFloodDuration } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'FormDrawer',
    props: ['floodDurationList'],
    components: { AntModal, UploadFile },
    data() {
      return {
        reservoirCurveTplUrl: 'http://thj.zjhhzk.cn:11119/thjgq/moban/%E8%AE%BE%E8%AE%A1%E6%B4%AA%E6%B0%B4%E8%BF%87%E7%A8%8B%E8%A1%A8.xlsx',
        parentCategoryOptions: [],

        loading: false,
        modalLoading: false,
        formTitle: '',
        // 表单参数
        form: {
          action: '1',
          durationName: undefined,
          floodDurationId: undefined,
          fileUrl: undefined,
        },
        open: false,

        rules: {
          durationName: [{ required: true, message: '洪水过程名称不能为空', trigger: 'blur' }],
          action: [{ required: true, message: '上传类型为空', trigger: 'blur' }],
          floodDurationId: [{ required: true, message: '洪水过程名称不能为空', trigger: 'change' }],
          fileUrl: [{ required: true, message: '文件不能为空', trigger: 'blur' }],
        },
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      add(item) {
        this.open = true
        this.formTitle = '上传洪水过程'
      },

      changeReservoir() {},
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      /** 提交按钮 */
      submitForm: function () {
        var that = this
        this.$refs.form.validate(valid => {
          if (valid) {
            this.$confirm({
              title: '确认上传洪水过程数据?',
              //     content: '当前选中编号为"' + names + '"的数据',
              onOk() {
                const saveForm = JSON.parse(JSON.stringify(that.form))
                that.loading = true

                if (that.form.action == 1) {
                  saveForm.floodDurationId = undefined
                  uploadCstDesignFloodDuration(saveForm)
                    .then(response => {
                      that.$message.success('新增成功', 3)
                      that.open = false
                      that.loading = false
                      that.$emit('ok')
                    })
                    .catch(err => (that.loading = false))
                } else {
                  uploadCstDesignFloodDuration(saveForm)
                    .then(response => {
                      that.$message.success('修改成功', 3)
                      that.open = false
                      that.loading = false
                      that.$emit('ok')
                    })
                    .catch(err => (that.loading = false))
                }
              },
              onCancel() {
                that.loading = false
              },
            })
          }
        })
      },
    },
  }
</script>
<style scoped lang="less">
  ::v-deep .upload-content .ant-upload-list-item-name {
    width: 420px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: -webkit-box !important;
    -webkit-box-orient: vertical !important;
    -webkit-line-clamp: 1 !important;
  }
  // ::v-deep .ant-upload-list-item-name .ant-upload-list-item-name-icon-count-1 {
  //   width: 200px !important;
  //   overflow: hidden !important;
  //   text-overflow: ellipsis !important;
  //   display: -webkit-box !important;
  //   -webkit-box-orient: vertical !important;
  //   -webkit-line-clamp: 1 !important;
  // }
</style>
